import request, { handleResponse } from '@/utils/request';

/**
 * 分页查询用户
 */
export async function pageUsers(params) {
  const res = await request.post('/api/system/user/listByPage', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询用户列表
 */
export async function listUsers(params) {
  const res = await request.get('/system/user', {
    params
  });
  if (res.data.code === 200 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询用户
 */
export async function getUser(id) {
  const res = await request.get('/api/system/user/detail?id=' + id);
  if (res.data.code === 200 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加用户
 */
export async function addUser(data) {
  return handleResponse(request.post('/system/user/add', data));
}

/**
 * 修改用户
 */
export async function updateUser(data) {
  return handleResponse(request.post('/system/user/update', data));
}

/**
 * 删除用户
 */
export async function removeUser(id) {
  return handleResponse(request.post('/system/user/delete', { id }));
}

/**
 * 批量删除用户
 */
export async function removeUsers(idList) {
  return handleResponse(request.post('/system/user/delete', { idList }));
}

/**
 * 修改用户状态
 */
export async function updateUserStatus(userId, status) {
  const res = await request.put('/system/user/status', {
    userId,
    status
  });
  if (res.data.code === 200) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 更新用户密码
 */
export async function updateUserPassword(userId, password = '123456') {
  const res = await request.put('/system/user/password', {
    userId,
    password
  });
  if (res.data.code === 200) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 重置用户密码
 */
export async function resetUserPassword(userId) {
  return handleResponse(request.post('/system/user/reset-password', { userId }));
}

/**
 * 导入用户
 */
export async function importUsers(file) {
  const formData = new FormData();
  formData.append('file', file);
  const res = await request.post('/system/user/import', formData);
  if (res.data.code === 200) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 导出用户
 */
export async function exportUsers(params) {
  const res = await request.get('/system/user/export', {
    params
  });
  return res.data.message;
}

/**
 * 检查用户是否存在
 */
export async function checkExistence(field, value, id) {
  const res = await request.get('/system/user/existence', {
    params: { field, value, id }
  });
  if (res.data.code === 200) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
