<!-- 编辑弹窗 -->
<template>
  <ele-modal form :width="560" v-model="visible" :title="isUpdate ? '修改年卡产品信息' : '新增年卡产品'" @open="handleOpen" destroy-on-close>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" @submit.prevent="">
      <el-form-item label="产品名称" prop="username">
        <el-input clearable v-model="form.username" placeholder="请输入分销商名称" />
      </el-form-item>
      <el-form-item label="产品描述" prop="realName">
        <el-input clearable v-model="form.realName" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="是否启用" prop="mobilePhone">
        <el-switch :active-value="0" :inactive-value="1" inline-prompt active-text="是"
        inactive-text="否" />
      </el-form-item>
      <el-form-item label="规则" prop="accountStatus">
        <el-input clearable v-model="form.mobilePhone" placeholder="请输入联系电话" />
        <!-- <dict-data code="accountStatus" v-model="form.accountStatus" type="radio" /> -->
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import { addUser, updateUser } from '@/api/system/user';
// import RoleSelect from '@/components/RoleSelect/index.vue';
import { encryptByMd5 } from '@/utils/crypto';

/** 修改回显的数据 */
const props = defineProps({
  data: Object
});

/** 定义事件 */
const emit = defineEmits(['update:modelValue', 'done']);

/** 弹窗是否打开 */
const visible = defineModel('modelValue');

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: null,
  username: null,
  realName: null,
  sex: null,
  mobilePhone: null,
  accountStatus: 1,
  avatar: null,
  roleIds: [],
  teamIds: [],
});

/** 表单验证规则 */
const rules = reactive({
  username: [
    {
      required: true,
      message: '请输入用户名',
      type: 'string',
      trigger: 'blur'
    }
  ],
  realName: [
    {
      required: true,
      message: '请输入姓名',
      type: 'string',
      trigger: 'blur'
    }
  ],
  sex: [
    {
      required: true,
      message: '请选择性别',
    }
  ],
  accountStatus: [
    {
      required: true,
      message: '请选择账号状态',
    }
  ],
});

/** 关闭弹窗 */
const handleCancel = () => {
  emit('update:modelValue', false);
};

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    const saveOrUpdate = isUpdate.value ? updateUser : addUser;
    // 如果有密码字段且不为空，则加密
    const userData = {
      ...form
    };
    if (userData.password) {
      userData.password = encryptByMd5(userData.password);
    }
    saveOrUpdate(userData)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

/** 弹窗打开事件 */
const handleOpen = () => {
  if (props.data) {
    const user = Object.assign({}, props.data);
    user.sex = String(user.sex);
    user.accountStatus = String(user.accountStatus);
    user.roleIds = user.roleList?.map(role => String(role.id));
    user.teamIds = user.teamList?.map(team => String(team.id));

    assignFields(user);
    isUpdate.value = true;
  } else {
    resetFields();
    isUpdate.value = false;
  }
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  });
};
</script>
