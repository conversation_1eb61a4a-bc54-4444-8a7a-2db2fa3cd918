<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="72px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="用户名">
            <el-input clearable v-model.trim="form.username" placeholder="请输入" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="角色">
            <role-select v-model="form.roleId" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商">
            <vendor-select v-model="form.vendorId" />
          </el-form-item>
        </el-col>

        <el-col v-if="more" :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="姓名">
            <el-input clearable v-model.trim="form.realName" placeholder="请输入" />
          </el-form-item>
        </el-col>


        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-link type="primary" :underline="false" @click="toggleMore">
              <span class="mx-2">更多</span>
              <el-icon v-if="!more">
                <IconElArrowDown />
              </el-icon>
              <el-icon v-else>
                <IconElArrowUp />
              </el-icon>
            </el-link>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { ref } from "vue";
import { useFormData } from "@/utils/use-form-data";
import RoleSelect from "@/components/RoleSelect/index.vue";
import VendorSelect from "@/components/VendorSelect/index.vue";

/** 定义事件 */
const emit = defineEmits(["search"]);

/** 表单数据 */
const [form, resetFields] = useFormData({
  username: null,
  realName: null,
  roleId: null,
  verdorId: null,
  teamId: null
});

/** 搜索 */
const search = () => {
  emit("search", { ...form });
};

/** 重置 */
const reset = () => {
  resetFields();
  search();
};


/** 更多 */
const more = ref(false);
const toggleMore = () => {
  more.value = !more.value;
};

</script>
