<template>
    <ele-drawer v-model="visible" title="年卡详情" :size="800" style="max-width: 100%;" @open="open" @close="close">
        <div class="subtitle mt--4"><span>基本信息</span></div>
        <el-descriptions :column="2" border>
            <el-descriptions-item label="年卡ID">
                <ele-copyable>{{ detail.cardId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="年卡卡号">
                <ele-copyable>{{ detail.cardNumber }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="产品ID">
                <ele-copyable v-if="detail.productId">{{ detail.productId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="产品名称">{{ detail.product?.productName }}</el-descriptions-item>

            <el-descriptions-item label="实体券">
                <ele-copyable v-if="detail.ticketId">{{ detail.ticketId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="券号">
                <ele-copyable v-if="detail.ticket">{{ detail.ticket?.ticketNumber }}</ele-copyable>
            </el-descriptions-item>

            <el-descriptions-item label="客户">
                <span class="mr-4">{{ detail.customerName }}</span>
                <span>{{ detail.mobile }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="证件号">{{ detail.idCard }}</el-descriptions-item>

            <el-descriptions-item label="激活分销商链">{{ detail.activationVendorChain }}</el-descriptions-item>
            <el-descriptions-item label="状态">
                <el-tag :type="statusEnum[detail.status]?.type">{{ statusEnum[detail.status]?.label }}</el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="激活时间">{{ detail.activationTime }}</el-descriptions-item>
            <el-descriptions-item label="到期日期">{{ detail.expiryDate }}</el-descriptions-item>

            <el-descriptions-item label="创建时间">{{ detail.createTime }}</el-descriptions-item>
            <el-descriptions-item label="创建人">{{ detail.createByUser?.username }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ detail.updateTime }}</el-descriptions-item>
            <el-descriptions-item label="更新人">{{ detail.updateByUser?.username }}</el-descriptions-item>
        </el-descriptions>

        <!-- 业务日志表格 -->
        <div class="subtitle mt-2"><span>日志记录</span></div>
        <ele-pro-table ref="tableRef" row-key="logId" :toolbar="false" :columns="logColumns"
            :datasource="serviceLogList" :pagination="false" :show-overflow-tooltip="false">
            <template #ticketNumber="{ row }">
                <span v-if="row.ticketNumber" type="primary" :underline="false">
                    {{ row.ticketNumber }}
                </span>
                <div v-else class="flex-y">
                    <span>{{ row.startNum }}</span>
                    <span>{{ row.endNum }}</span>
                </div>
            </template>
            <template #serviceType="{ row }">
                <dict-data type="text" code="serviceType" v-model="row.serviceType" />
            </template>
        </ele-pro-table>
    </ele-drawer>
</template>

<script setup>
import { defineProps, defineEmits, computed, reactive, ref } from 'vue';
import { requestCardDetail } from '@/api/distro/card/index';
import { requestCardServiceLogList } from '@/api/distro/service-log/index';
import { ElMessage } from 'element-plus';

const props = defineProps({
    visible: { type: Boolean, default: false },
    data: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['close'])

const statusEnum = {
    'Active': {
        type: 'success',
        label: '有效'
    },
    'Expired': {
        type: 'danger',
        label: '已过期'
    },
    'Cancelled': {
        type: 'info',
        label: '已注销'
    }
}

const visible = computed({
    get() { return props.visible },
    set() { }
})

let detail = reactive({
    cardId: "",
    cardNumber: "",
    thirdPartyUserId: "",
    userInfo: "",
    activationTicketId: "",
    activationTicketNumber: "",
    activationProductId: null,
    activationProductName: "",
    activationVendorChain: "",
    activationTime: "",
    expiryDate: "",
    status: "",
    createTime: "",
    updateTime: "",
});

/** 表格实例 */
const tableRef = ref(null);

/** 业务日志表格列配置 */
const logColumns = ref([
    {
        prop: 'ticketNumber',
        label: '实体券',
        align: 'left',
        width: 200,
        slot: 'ticketNumber'
    },
    {
        prop: 'ticketCount',
        label: '数量',
        align: 'center',
        width: 60
    },
    {
        prop: 'cardNumber',
        label: '年卡',
        align: 'center',
        width: 180
    },
    {
        prop: 'serviceType',
        label: '类型',
        align: 'left',
        width: 100,
        slot: 'serviceType'
    },
    {
        prop: 'description',
        label: '描述',
        align: 'left',
        minWidth: 200
    },
    {
        prop: 'remark',
        label: '备注',
        align: 'left',
        minWidth: 60
    },
    {
        prop: 'vendorName',
        label: '分销商',
        align: 'center',
        minWidth: 90
    },
    {
        prop: 'createByUsername',
        label: '操作员',
        align: 'center',
        minWidth: 90
    },
    {
        prop: 'createTime',
        label: '日志时间',
        align: 'center',
        fixed: 'right',
        width: 160
    }
]);

/** 表格数据源 */
const serviceLogList = ({ where }) => {
    return requestCardServiceLogList({
        ...where,
        cardId: detail.cardId,
    });
};



async function open() {
    try {
        // 重置 detail 数据，确保每次打开时数据都是最新的
        Object.assign(detail, {
            cardId: "",
            cardNumber: "",
            thirdPartyUserId: "",
            userInfo: "",
            activationTicketId: "",
            activationTicketNumber: "",
            activationProductId: null,
            activationProductName: "",
            activationVendorChain: "",
            activationTime: "",
            expiryDate: "",
            status: "",
            createTime: "",
            updateTime: "",
        });

        let { cardId, cardNumber } = props.data;
        if (!cardId && !cardNumber) {
            throw { message: '获取编号失败' }
        }
        let result = await requestCardDetail({ cardId, cardNumber });
        Object.assign(detail, result);

        // 刷新业务日志表格
        tableRef.value?.reload?.();
    } catch (error) {
        let content = error.message || '获取数据失败';
        ElMessage.error(content);
    }
}

function close() {
    emit('close');
}
</script>

<style scoped lang="scss">
.flex-y {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
</style>