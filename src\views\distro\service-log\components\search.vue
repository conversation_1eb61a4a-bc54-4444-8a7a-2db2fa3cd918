<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form :model="where" ref="formRef" label-width="60px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="类型" prop="serviceType">
            <dict-data code="serviceType" v-model="where.serviceType" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商" prop="vendorId">
            <vendor-select v-model="where.vendorId" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="券号" prop="ticketNumber">
            <el-input v-model="where.ticketNumber" placeholder="请输入券号" clearable />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="卡号" prop="cardNumber">
            <el-input v-model="where.cardNumber" placeholder="请输入卡号" clearable />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="产品" prop="productId">
            <product-select v-model="where.productId" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="操作人" prop="username">
            <el-input v-model="where.username" placeholder="请输入操作人" clearable />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="时间" prop="createTimeRange">
            <date-range-picker v-model="dateRange" @change="handleDateChange" />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">
              查询
            </el-button>
            <el-button @click="reset">
              重置
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import VendorSelect from '@/components/VendorSelect/index.vue';
import ProductSelect from '@/components/ProductSelect/index.vue';
import dayjs from 'dayjs';
import DateRangePicker from '@/components/DateRangePicker/index.vue';

/** 搜索条件 */
const where = reactive({
  serviceType: '',
  status: '',
  ticketId: '',
  cardId: '',
  vendorId: '',
  productId: '',
  username: '',
  startTime: dayjs().startOf('day').format('YYYY-MM-DD'),
  endTime: dayjs().endOf('day').format('YYYY-MM-DD')
});

/** 表单实例 */
const formRef = ref(null);

/** 日期范围 */
const dateRange = ref([
  dayjs().startOf('day').format('YYYY-MM-DD'),
  dayjs().endOf('day').format('YYYY-MM-DD')
]);

/** 监听日期选择变化 */
watch(
  () => dateRange.value,
  (val) => {
    if (val) {
      where.startTime = val[0];
      where.endTime = val[1];
    } else {
      where.startTime = '';
      where.endTime = '';
    }
  },
);

// 定义事件
const emit = defineEmits(['search']);

/** 搜索 */
const search = () => {
  emit('search', { ...where });
};

/** 重置 */
const reset = () => {
  formRef.value?.resetFields();
  dateRange.value = [
    dayjs().startOf('day').format('YYYY-MM-DD'),
    dayjs().endOf('day').format('YYYY-MM-DD')
  ];
  where.startTime = '';
  where.endTime = '';
  emit('search', { ...where });
};

/** 处理日期变化 */
const handleDateChange = (val) => {
  if (val) {
    where.startTime = val[0];
    where.endTime = val[1];
  } else {
    where.startTime = '';
    where.endTime = '';
  }
};
</script>
