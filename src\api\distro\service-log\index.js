import request from '@/utils/request';

/**
 * @async
 * @description 查询业务日志列表 (分页)
 */
export async function requestServiceLogList(params) {
  const res = await request.get('/api/distro/service-log/list', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 查询指定年卡的业务日志列表
 */
export async function requestCardServiceLogList(params) {
  const res = await request.get('/api/distro/service-log/list-by-card', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * @async   
 * @description 获取业务日志详细信息
 * @param { Object } params
 * @param { String } params.logId 业务日志ID (必填)
 */
export async function requestServiceLogDetail(params) {
  const res = await request.get('/api/distro/service-log/detail', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
