<template>
    <ele-drawer v-model="visible" title="产品详情" :size="600" style="max-width: 100%;" @open="open" @close="close">
        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">规则集ID：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.ruleSetId }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">规则集名称：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.ruleSetName }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">描述：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.description }}</div>
            </el-col>
        </el-row>
        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">是否启用默认规则：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.isDefault ? '已启用' : '未启用' }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">创建人ID：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.createBy }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">创建时间：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.createTime }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">更新人ID：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.updateBy }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">更新时间：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.updateTime }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">层级规则列表：</div>
            </el-col>
            <el-col :span="18" />
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">佣金类型：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ FeeType[detail.feeType] }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="24">
                <div class="grid-content ep-bg-purple">
                    <el-table :data="detail.levels" border size="small">
                        <el-table-column prop="ruleLevelId" align="center" label="规则ID" min-width="60" />
                        <el-table-column prop="level" align="center" label="层级" min-width="20" />
                        <el-table-column prop="feeType" align="center" label="佣金类型" min-width="40">
                            <template #default="{ row }">
                                <span v-if="row.feeType === 'Percentage'">
                                    百分比
                                </span>
                                <span v-else-if="row.feeType === 'FixedAmount'">
                                    固定金额
                                </span>
                                <span v-else>
                                    无
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="feeValue" align="center" label="佣金值">
                            <template #default="{ row }">
                                <span v-if="row.feeType === 'Percentage'">
                                    {{ row.feeValue }}%
                                </span>
                                <span v-else-if="row.feeType === 'FixedAmount'">
                                    {{ row.feeValue }}
                                </span>
                                <span v-else>
                                    无
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-col>
        </el-row>
    </ele-drawer>
</template>

<script setup>
import { defineProps, defineEmits, computed, reactive } from 'vue';
import { requestFeeRuleDetail } from '@/api/distro/fee-rule/index';
import { ElMessage } from 'element-plus';

const props = defineProps({
    visible: { type: Boolean, default: false },
    data: { type: Object, default: () => ({}) }
})

const FeeType = {
    Percentage: '百分比', // 百分比
    FixedAmount: '固定金额', // 固定金额
    NoFee: 'NoFee' // 无
}

const emit = defineEmits(['close'])

const visible = computed({
    get() { return props.visible },
    set() { }
})

let detail = reactive({
    ruleSetId: "",
    ruleSetName: "",
    isDefault: "",
    description: false,
    createBy: "",
    levels: [],
    updateBy: "",
    createTime: "",
    updateTime: "",
});

async function open() {
    try {
        // 重置 detail 数据，确保每次打开时数据都是最新的
        Object.assign(detail, {
            ruleSetId: "",
            ruleSetName: "",
            isDefault: "",
            description: false,
            createBy: "",
            levels: [],
            updateBy: "",
            createTime: "",
            updateTime: "",
        });
        let { ruleSetId } = props.data;
        console.log(ruleSetId, 'ruleSetId');

        if (!ruleSetId) {
            throw { message: '获取编号失败' }
        }
        let result = await requestFeeRuleDetail({ ruleSetId })
        Object.assign(detail, result); // 合并数据到 detail 中，覆盖已有属性，新增属性会被添加到 detail 中
    } catch (error) {
        let content = error.message || '获取数据失败';
        ElMessage.error(content);
    }
}

function close() {
    emit('close');
}
</script>