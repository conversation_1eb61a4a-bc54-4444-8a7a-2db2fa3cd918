<template>
  <el-date-picker
    v-model="dateRange"
    type="daterange"
    :value-format="valueFormat"
    :format="format"
    :start-placeholder="startPlaceholder"
    :end-placeholder="endPlaceholder"
    :shortcuts="shortcuts"
    @change="handleChange"
  />
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import dayjs from 'dayjs';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  valueFormat: {
    type: String,
    default: 'YYYY-MM-DD'
  },
  format: {
    type: String,
    default: 'YYYY-MM-DD'
  },
  startPlaceholder: {
    type: String,
    default: '开始日期'
  },
  endPlaceholder: {
    type: String,
    default: '结束日期'
  },
  // 自定义快捷选项
  customShortcuts: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const dateRange = ref(props.modelValue);

// 默认快捷选项
const defaultShortcuts = [
  {
    text: '今天',
    value: () => {
      return [
        dayjs().startOf('day').format(props.valueFormat),
        dayjs().endOf('day').format(props.valueFormat)
      ]
    }
  },
  {
    text: '昨天',
    value: () => {
      return [
        dayjs().subtract(1, 'day').startOf('day').format(props.valueFormat),
        dayjs().subtract(1, 'day').endOf('day').format(props.valueFormat)
      ]
    }
  },
  {
    text: '最近3天',
    value: () => {
      return [
        dayjs().subtract(2, 'day').startOf('day').format(props.valueFormat),
        dayjs().endOf('day').format(props.valueFormat)
      ]
    }
  },
  {
    text: '最近1周',
    value: () => {
      return [
        dayjs().subtract(6, 'day').startOf('day').format(props.valueFormat),
        dayjs().endOf('day').format(props.valueFormat)
      ]
    }
  },
  {
    text: '本月',
    value: () => {
      return [
        dayjs().startOf('month').format(props.valueFormat),
        dayjs().endOf('month').format(props.valueFormat)
      ]
    }
  },
  {
    text: '上一月',
    value: () => {
      return [
        dayjs().subtract(1, 'month').startOf('month').format(props.valueFormat),
        dayjs().subtract(1, 'month').endOf('month').format(props.valueFormat)
      ]
    }
  },
  {
    text: '本年',
    value: () => {
      return [dayjs().startOf('year').format(props.valueFormat), dayjs().endOf('year').format(props.valueFormat)];
    }
  },
  {
    text: '上一年',
    value: () => {
      return [dayjs().subtract(1, 'year').startOf('year').format(props.valueFormat), dayjs().subtract(1, 'year').endOf('year').format(props.valueFormat)];
    }
  }
];

// 合并默认和自定义快捷选项
const shortcuts = computed(() => {
  return props.customShortcuts.length > 0 ? props.customShortcuts : defaultShortcuts;
});

// 监听值变化
watch(
  () => props.modelValue,
  (val) => {
    dateRange.value = val;
  }
);

// 处理日期变化
const handleChange = (val) => {
  emit('update:modelValue', val);
  emit('change', val);
};
</script> 