<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="productId" :columns="columns" :datasource="renewalList" :pagination="true"
        :show-overflow-tooltip="false">
        
        <template #feeProcessed="{ row }">
          <ele-dot v-if="row.feeProcessed" type="success" text="已结算" :ripple="false" />
          <ele-dot v-else type="danger" text="未结算" />
        </template>

        <template #action="{ row }">
          <template v-if="!row.feeProcessed">
            <el-popconfirm
              title="确定要结算该续期记录吗？"
              confirm-button-text="确定"
              cancel-button-text="取消"
              @confirm="handleSettle(row)"
              :width="240"
              placement="left"
            >
              <template #reference>
                <el-link type="primary" :underline="false">结算</el-link>
              </template>
            </el-popconfirm>
            <el-divider direction="vertical" />
          </template>
          <el-link type="primary" :underline="false" @click="showBatch(row)">
            查看
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            同步
          </el-link>            
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <user-edit v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import Search from './components/search.vue';
import UserEdit from './components/user-edit.vue';
import { requestRenewalList, requestSettleRenewal } from '@/api/distro/renewal/index';

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([  
  {
    prop: 'renewalId',
    label: '续期ID',
    align: 'center',
    hideInTable: true,
    width: 180
  },
  {
    prop: 'cardId',
    label: '关联年卡ID',
    align: 'center',
    hideInTable: true,
    width: 180,
  },
  {
    prop: 'cardNumber',
    label: '年卡卡号',
    align: 'left',
    minWidth: 180
  },
  {
    prop: 'productName',
    label: '产品名称',
    align: 'center',
  },
  {
    prop: 'openTicketNumber',
    label: '开卡券号',
    align: 'left',
    minWidth: 180
  },
  {
    prop: 'openVendorName',
    label: '开卡分销商',
    align: 'left',
    minWidth: 100
  },  
  {
    prop: 'renewalAmount',
    label: '续期金额',
    align: 'center',
  },
  
  {
    prop: 'feeProcessed',
    label: '结算状态',
    align: 'center',
    minWidth: 60,
    slot: 'feeProcessed'
  },
  {
    prop: 'renewalTime',
    label: '续期时间',
    align: 'center',
    width: 180
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    hideInTable: true,
    width: 180,
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 120,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 是否显示详情 */
let showDetail = ref(false);

/** 表格数据源 */
const renewalList = ({ pages, where, orders, filters }) => {
  return requestRenewalList({
    ...pages,
    ...where,
    ...orders,
    ...filters
  });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 查看数据 */
const showBatch = (row) => {
  console.log(row, 'show');
  // 获取id
  showDetail.value = true;
};

/** 处理结算操作 */
const handleSettle = async (row) => {
  try {
    await requestSettleRenewal({
      renewalId: row.renewalId
    });
    ElMessage.success('结算成功');
    reload();
  } catch (error) {
    ElMessage.error(error.message || '结算失败');
  }
};
</script>
