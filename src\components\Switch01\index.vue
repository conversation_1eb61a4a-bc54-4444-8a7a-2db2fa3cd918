<template>
  <el-switch v-model="switchValue" :active-value="1" :inactive-value="0" @change="handleChange" />
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: [Number, String, null],
    default: 0
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const switchValue = computed({
  get: () => {
    const val = props.modelValue;
    if (val === null) return 0;
    return typeof val === 'string' ? parseInt(val) || 0 : val;
  },
  set: (val) => {
    emit('update:modelValue', val);
  }
});

const handleChange = (val) => {
  emit('change', val);
};
</script>
