<template>
    <ele-drawer v-model="visible" title="号段详情" :size="600" style="max-width: 100%;" @open="open" @close="close">
        <el-descriptions :column="2" class="detail-descriptions">
            <el-descriptions-item label="号段ID">
                <ele-copyable>{{ detail.segmentId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="产品名称">
                {{ detail.product?.productName }}
            </el-descriptions-item>
            <el-descriptions-item label="产品编码">
                <ele-copyable>{{ detail.product?.productGid }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="产品ID">
                <ele-copyable>{{ detail.product?.productId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="起始券号">
                {{ detail.startNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="结束券号">
                {{ detail.endNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="总数量">
                {{ detail.totalCount }}
            </el-descriptions-item>
            <el-descriptions-item label="已分配">
                {{ detail.assignedCount }}
            </el-descriptions-item>
            <el-descriptions-item label="分销商">
                {{ detail.vendor?.name || '未分配' }}
            </el-descriptions-item>
            <el-descriptions-item label="分销商ID">
                <ele-copyable v-if="detail.vendor?.vendorId">{{ detail.vendor?.vendorId }}</ele-copyable>                
            </el-descriptions-item>
            <el-descriptions-item label="是否可用">
                <el-tag :type="detail.isActive ? 'success' : 'danger'">
                    {{ detail.isActive ? '已启用' : '未启用' }}
                </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="分配时间" :span="2">
                {{ detail.createTime }}
            </el-descriptions-item>
        </el-descriptions>        
    </ele-drawer>
</template>

<script setup>
import { defineProps, defineEmits, computed, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { requestTicketSegmentDetail } from '@/api/distro/ticket-segment/index';

const props = defineProps({
    visible: { type: Boolean, default: false },
    data: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['close'])

const visible = computed({
    get() { return props.visible },
    set() { }
})

let detail = reactive({
    segmentId: "",
    productId: "",
    productName: "",
    productGid: "",
    startNumber: "",
    endNumber: "",
    totalCount: 0,
    assignedToVendorName: "",
    isActive: false,
    createTime: "",
});

async function open() {
    try {
        // 重置 detail 数据
        Object.assign(detail, {
            segmentId: "",
            productId: "",
            productName: "",
            productGid: "",
            startNumber: "",
            endNumber: "",
            totalCount: 0,
            assignedToVendorName: "",
            isActive: false,
            createTime: "",
        });
        
        let { segmentId } = props.data;
        if (!segmentId) {
            throw { message: '号段ID为空' }
        }
        
        // 获取号段详情
        const result = await requestTicketSegmentDetail({ segmentId });
        Object.assign(detail, result);
    } catch (error) {
        let content = error.message || '获取数据失败';
        ElMessage.error(content);
    }
}

function close() {
    emit('close');
}
</script>

<style scoped lang="scss">
.detail-descriptions {
    :deep(.el-descriptions__label) {                
        &::after {
          content: ' : ';
        }
        font-weight: bold;
    }
}
</style>