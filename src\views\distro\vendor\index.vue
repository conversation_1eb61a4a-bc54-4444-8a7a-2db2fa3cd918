<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <ele-pro-table ref="tableRef" :height="480" :virtual="true" row-key="vendorId" :columns="columns"
        default-expand-all :datasource="vendorList" :lazy="true" :pagination="true" :show-overflow-tooltip="false">
        <template #toolbar>
          <!-- 管理员新建一级分销商 -->
          <el-button type="primary" class="ele-btn-icon" :icon="PlusOutlined" @click="openCreate()">
            新建分销商
          </el-button>
          <el-button class="ele-btn-icon" :icon="ColumnHeightOutlined" @click="expandAll">
            展开全部
          </el-button>
          <el-button class="ele-btn-icon" :icon="VerticalAlignMiddleOutlined" @click="foldAll">
            折叠全部
          </el-button>
        </template>

        <template #status="{ row }">
          <el-tag :type="statusList[row.status].type">{{ statusList[row.status].label }}</el-tag>
        </template>

        <template #level="{ row }">
          <el-tag :type="getLevelTagType(row.level)">{{ getLevelText(row.level) }}</el-tag>
        </template>

        <template #createTime="{ row }">
          <span>{{ Dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>

        <template #action="{ row }">
          <template v-if="row.username !== 'admin'">
            <el-link v-permission="'system:user:remove'" type="primary" :underline="false" @click="openCreate(row)">
              创建
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <el-link v-permission="'system:user:edit'" type="primary" :underline="false" @click="openEdit(row)">
              修改
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <el-link v-permission="'system:user:remove'" type="danger" :underline="false" @click="removeBatch(row)">
              删除
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <Edit v-model="showEdit" :data="current" @done="reload" />
    <Create v-model="showCreate" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
import { ref, reactive } from 'vue';
import {
  PlusOutlined,
  ColumnHeightOutlined,
  VerticalAlignMiddleOutlined,
} from '@/components/icons';
import { ElMessageBox } from 'element-plus/es';
import { eachTree, EleMessage, toTree } from 'ele-admin-plus/es';
import Search from './components/search.vue';
import Edit from './components/edit.vue';
import Create from './components/create.vue';
import Dayjs from 'dayjs';
import { requestVendorList, requestVendorDelete } from '@/api/distro/vendor/index';
/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([

  {
    prop: 'name',
    label: '名称',
    sortable: 'custom',
    minWidth: 160,
    align: 'left',
  },
  {
    prop: 'vendorId',
    label: '分销商ID',
    sortable: 'custom',
    align: 'center',
    width: 200,
    hideInTable: true,
  },  
  {
    prop: 'contactPerson',
    label: '联系人',
    width: 90,
    align: 'center',    
  },
  {
    prop: 'phoneNumber',
    label: '联系电话',
    width: 90,
    align: 'center',
  },
  {
    prop: 'level',
    label: '级别',
    width: 90,
    align: 'center',
    slot: 'level',
  },
  {
    prop: 'parentVendorId',
    label: '上级分销商ID',
    width: 200,
    align: 'center',
    hideInTable: true,
  },
  {
    prop: 'availableFeeBalance',
    label: '可提现（元）',
    width: 140,
    align: 'center',
  },
  {
    prop: 'totalFeeEarned', 
    label: '累计佣金（元）',
    width: 140,
    align: 'center',
  },
  {
    prop: 'totalFeeWithdrawn',
    label: '已提现（元）',
    width: 140,
    align: 'center',
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    width: 90,
    slot: 'status',
    filterMultiple: false,
    filters: [
      {
        text: '已激活',
        value: 'Active'
      },
      {
        text: '未激活',
        value: 'Inactive'
      },
      {
        text: '停用',
        value: 'Suspended'
      }
    ],
  },
  {
    prop: 'createTime',
    label: '创建时间',
    slot: 'createTime',
    sortable: 'custom',
    align: 'center',
    hideInTable: true,
    width: 220,
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 180,
    align: 'center',
    slot: 'action',
    fixed: 'right',    
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 分销商激活状态 */
const statusList = reactive({
  Active: {
    label: '已激活',
    type: 'success'
  },
  Inactive: {
    label: '未激活',
    type: 'danger'
  },
  Suspended: {
    label: '已停用',
    type: 'warning'
  },
});

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 是否显示编辑弹窗 */
const showCreate = ref(false);

/** 表格数据源 */
const vendorList = async ({ pages, where, orders, filters }) => {
  const data = await requestVendorList({
    ...pages,
    ...where,
    ...orders,
    ...filters,
  })

  const treeData = toTree({
    data: data.rows,
    idField: 'vendorId',
    parentIdField: 'parentVendorId'
  });
  eachTree(treeData, (node) => {
    node.hasChildren = node.children?.length > 0;
  });
  return treeData;
};

const getLevelText = (level) => {
  switch (String(level)) {
    case '1':
      return '一级';
    case '2':
      return '二级';
    case '3':
      return '三级';
    case '4':
      return '四级';
    case '5':
      return '五级';
    case '6':
      return '六级';
    default:
      return '未知';
  }
}

const getLevelTagType = (level) => {
  switch (String(level)) {
    case '1':
      return 'primary';
    case '2':
      return 'success';
    case '3':
      return 'warning';
    case '4':
      return 'danger';
    case '5':
      return 'info';
    default:
      return 'default';
  }
}

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 打开创建弹窗 */
const openCreate = (row) => {
  current.value = row ?? null;
  showCreate.value = true;
};

/** 批量删除 */
const removeBatch = (row) => {
  console.log(row, 'row')
  let { name, vendorId } = row;
  if (!vendorId.length) {
    EleMessage.error('获取id失败');
    return;
  }
  ElMessageBox.confirm(
    '是否确认删除【' + name + '】的数据项?',
    '系统提示',
    { type: 'warning', draggable: true }
  )
    .then(() => {
      const loading = EleMessage.loading({
        message: '请求中..',
        plain: true
      });
      requestVendorDelete({ vendorId })
        .then(() => {
          loading.close();
          EleMessage.success('删除成功');
          reload();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    })
    .catch(() => { });
};

/** 展开全部 */
const expandAll = () => {
  tableRef.value?.toggleRowExpansionAll?.(true);
};

/** 折叠全部 */
const foldAll = () => {
  tableRef.value?.toggleRowExpansionAll?.(false);
};
</script>
