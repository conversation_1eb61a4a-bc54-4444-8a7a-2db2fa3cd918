<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="productId" :columns="columns" :datasource="cardList" :pagination="true"
        :show-overflow-tooltip="false">
        <template #status="{ row }">
          <el-tag :type="StatusEnum[row.status].type">{{ StatusEnum[row.status].label }}</el-tag>
        </template>

        <template #action="{ row }">
          <template v-if="row.username !== 'admin'">
            <el-link v-permission="'system:user:remove'" type="primary" :underline="false" @click="handleDetail(row)">
              查看
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <el-link v-permission="'system:user:edit'" type="primary" :underline="false" @click="openEdit(row)">
              同步
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <user-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 详情内容 - 抽屉 -->
    <Detail :visible="showDetail" :data="current" @close="onClose" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import Search from './components/search.vue';
import UserEdit from './components/user-edit.vue';
import Detail from './components/detail.vue';
import { requestCardList } from '@/api/distro/card/index';
/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'cardId',
    label: '年卡ID',
    align: 'center',
    hideInTable: true,
    width: 200
  },
  {
    prop: 'cardNumber',
    label: '卡号',
    align: 'left',
    width: 160,
  },
  {
    prop: 'productName',
    label: '产品名称',
    align: 'center',
    minWidth: 120,
  },
  {
    prop: 'customerName',
    label: '客户姓名',
    align: 'center',
    width: 90,
  },
  {
    prop: 'idCard', 
    label: '身份证号',
    align: 'center',
    width: 160,
  },
  {
    prop: 'mobile',
    label: '手机号',
    align: 'center',
    hideInTable: true,
    width: 100,
  }, 
  {
    prop: 'activationTime',
    label: '激活时间',
    align: 'center',    
    width: 160,
  },
  {
    prop: 'expiryDate',
    label: '到期日期',
    align: 'center',
    minWidth: 110,
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    minWidth: 90,
    slot: 'status',
    filters: [
      { text: '已激活', value: 'Active' },
      { text: '已过期', value: 'Expired' },
      { text: '已取消', value: 'Cancelled' }
    ],
    filterMultiple: false
  },
  {
    prop: 'ticketNumber',
    label: '实体券',
    align: 'center',
    width: 160,
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 120,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

const StatusEnum = {
  Active: {
    label: '已激活',
    type: 'success'
  },
  Expired: {
    label: '已过期',
    type: 'danger'
  },
  Cancelled: {
    label: '已取消',
    type: 'error'
  },
}

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 是否显示详情 */
let showDetail = ref(false);

/** 表格数据源 */
const cardList = ({ pages, where, filters }) => {
  const status = filters?.status?.[0];
  return requestCardList({
    ...pages,
    ...where,
    status
  });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 查看数据 */
const handleDetail = (row) => {    
  current.value = row ?? {};
  showDetail.value = true;
}

/** 关闭详情 */
const onClose = () => {
  showDetail.value = false;
}
</script>
