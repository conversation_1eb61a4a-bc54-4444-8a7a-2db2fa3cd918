import request, { handleResponse } from '@/utils/request';
import { download, toFormData, checkDownloadRes } from '@/utils/common';

const baseURL = '/api/system/sample';

/**
 * 分页查询样例
 * @param {Object} params 查询参数
 * @returns {Promise<Object>}
 */
export function getSampleList(params) {
  return handleResponse(request.get(baseURL + `/list`, { params }));
}

/**
 * 根据id查询样例
 * @param {string} id 样例id
 * @returns {Promise<Object>}
 */
export function getSample(id) {
  return handleResponse(request.get(baseURL + `/detail/?id=${id}`));
}

/**
 * 添加样例
 * @param {Object} data 样例数据
 * @returns {Promise<void>}
 */
export function addSample(data) {
  return handleResponse(request.post(baseURL + `/add`, data));
}

/**
 * 修改样例
 * @param {Object} data 样例数据
 * @returns {Promise<void>}
 */
export function updateSample(data) {
  return handleResponse(request.post(baseURL +`/update`, data));
}

/**
 * 删除样例
 * @param {string} id 样例id
 * @returns {Promise<void>}
 */
export function removeSample(id) {
  return handleResponse(request.post(baseURL + `/delete`, { id }));
}

/**
 * 批量删除样例
 * @param {string[]} ids 样例id集合
 * @returns {Promise<void>}
 */
export function removeSamples(idList) {
  return handleResponse(request.post(baseURL + `/delete`, { idList }));
}

/**
 * 导出样例
 * @param {Object} params 导出参数
 * @returns {Promise<void>}
 */
export function exportSamples(params) {
  return request({
    url: baseURL + `/export`,
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  }).then(async (res) => {
    await checkDownloadRes(res);
    download(res.data, `sample_${Date.now()}.xlsx`);
  });
} 