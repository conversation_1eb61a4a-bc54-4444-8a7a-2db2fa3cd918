<template>
  <ele-page>
    <!-- 搜索表单 -->
    <sample-search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        sticky
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :highlight-current-row="true"
        :export-config="{ fileName: '样例' }"
        :default-expand-all="true"
        cache-key="systemSampleTable"
      >
        <template #toolbar>
          <el-button
            v-permission="'system:sample:add'"
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            v-permission="'system:sample:delete'"
            type="danger"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            删除
          </el-button>

        </template>
        <template #action="{ row }">
          <el-link
            v-permission="'system:sample:add'"
            type="primary"
            :underline="false"
            @click="openEdit(null, row.id)"
          >
            添加
            <el-divider direction="vertical" />
          </el-link>

          <el-link
            v-permission="'system:sample:edit'"
            type="primary"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
            <el-divider direction="vertical" />
          </el-link>

          <el-link
            v-permission="'system:sample:remove'"
            type="danger"
            :underline="false"
            @click="remove(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <sample-edit
      v-model="showEdit"
      :data="current"
      :parent-id="parentId"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
import { ref } from "vue";
import { PlusOutlined } from "@/components/icons";
import { ElMessageBox } from "element-plus/es";
import { EleMessage } from "ele-admin-plus/es";
import SampleSearch from "./components/sample-search.vue";
import SampleEdit from "./components/sample-edit.vue";
import { getSampleList, removeSamples } from "@/api/system/sample";

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: "selection",
    columnKey: "selection",
    width: 50,
    align: "center",
    fixed: "left"
  },
  {
    prop: "title",
    label: "标题",
    align: "left",
    minWidth: 110
  },
  {
    prop: "content",
    label: "内容",
    align: " center",
    minWidth: 110
  },
  {
    prop: "parentId",
    label: "上级id",
    align: " center",
    minWidth: 110
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: " center",
    minWidth: 110
  },
  {
    prop: "updateTime",
    label: "更新时间",
    align: " center",
    minWidth: 110
  },
  {
    columnKey: "action",
    label: "操作",
    width: 180,
    align: "center",
    slot: "action",
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 上级菜单id */
const parentId = ref();

/** 表格数据源 */
const datasource = async ({ pages, where }) => {
  return await getSampleList({ ...where, ...pages });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ where });
};

/** 打开编辑弹窗 */
const openEdit = (row, id) => {
  current.value = row ?? null;
  parentId.value = id;
  showEdit.value = true;
};

/** 删除单个 */
const remove = (row) => {
  const rows = row === null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error("请至少选择一条数据");
    return;
  }
  ElMessageBox.confirm(
    `确认删除【${rows.map(it => it.title).join(",")}】吗?`,
    "系统提示",
    { type: "warning", draggable: true }
  )
    .then(() => {
      const loading = EleMessage.loading({
        message: "请求中..",
        plain: true
      });
      removeSamples(rows.map((it) => {
        it.id;
      }))
        .then(() => {
          loading.close();
          EleMessage.success("删除成功");
          reload();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    })
    .catch(() => {
    });
};

/** 展开全部 */
const expandAll = () => {
  tableRef.value?.toggleRowExpansionAll?.(true);
};

/** 折叠全部 */
const foldAll = () => {
  tableRef.value?.toggleRowExpansionAll?.(false);
};
</script>
