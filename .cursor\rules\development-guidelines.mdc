---
description: 
globs: *.vue,*.js
alwaysApply: false
---
# 开发规范指南

## 项目结构说明

### 核心文件
- [App.vue](mdc:src/App.vue) - 应用程序根组件
- [main.js](mdc:src/main.js) - 应用程序入口文件

### 目录结构
- `src/api/` - API接口定义和请求封装
- `src/assets/` - 静态资源文件（图片、字体等）
- `src/components/` - 可复用组件，公共组件
- `src/config/` - 配置文件
- `src/i18n/` - 国际化资源
- `src/layout/` - 页面布局组件
- `src/router/` - 路由配置
- `src/store/` - Vuex状态管理
- `src/styles/` - 全局样式文件
- `src/utils/` - 工具函数
- `src/views/` - 页面级组件

## 开发规范

### 命名规范
1. 组件文件名：使用PascalCase（如：`UserProfile.vue`）
2. 路由文件名：使用kebab-case（如：`user-routes.js`）
3. 工具函数文件名：使用camelCase（如：`dateUtils.js`）

### 组件开发规范
1. 组件应当放在适当的目录中：
   - 通用组件放在 `components/`
   - 页面组件放在 `views/`
   - 布局组件放在 `layout/`
2. 组件应当具有清晰的职责划分
3. 保持组件的单一职责原则

### API开发规范
1. API请求统一在 `api/` 目录下管理
2. 使用统一的请求封装
3. API命名应当清晰表达其用途

### 状态管理规范
1. 全局状态统一在 `store/` 目录下管理
2. 按模块划分状态管理文件
3. 遵循Vuex的使用规范

### 样式开发规范
1. 全局样式放在 `styles/` 目录
2. 组件样式应当采用scoped或module方式
3. 遵循项目既定的CSS命名规范

### 国际化开发规范
1. 所有文案都应该使用国际化配置
2. 国际化资源文件放在 `i18n/` 目录下
3. 按语言和模块组织国际化资源

## 代码质量要求
1. 提交代码前必须通过ESLint检查
2. 保持代码格式统一，使用项目配置的Prettier规则
3. 编写必要的注释，特别是对于复杂的业务逻辑
4. 遵循代码复用原则，避免重复代码




