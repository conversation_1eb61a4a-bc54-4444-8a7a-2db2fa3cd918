<!-- 编辑弹窗 -->
<template>
  <div>
    <ele-modal form :width="560" v-model="visible" :title="isUpdate ? '修改佣金规则集' : '新增佣金规则集'" @open="handleOpen"
      destroy-on-close>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" @submit.prevent="">
       <el-form-item v-if="form.ruleSetId" label="规则集ID" prop="ruleSetId">
          <el-input clearable disabled v-model="form.ruleSetId" placeholder="请输入ID" />
        </el-form-item>
       
        <el-form-item label="规则集名称" prop="ruleSetName">
          <el-input clearable v-model="form.ruleSetName" placeholder="请输入名称" />
        </el-form-item>
        
        <el-form-item label="产品描述" prop="description">
          <el-input clearable v-model="form.description" resize="none" type="textarea" :rows="5" placeholder="请输入描述" />
        </el-form-item>

        <el-form-item label="层级规则" prop="description">
          <ele-data-table v-if="form.levels && form.levels.length" ref="tableRef" border row-key="level" size="small"
            :bottomLine="false" :columns="columns" :data="form.levels">
            <template #feeType="{ row }">
              <span v-if="row.feeType === 'Percentage'">
                百分比
              </span>
              <span v-else-if="row.feeType === 'FixedAmount'">
                固定金额
              </span>
              <span v-else>
                无
              </span>
            </template>

            <template #feeValue="{ row }">
              <span v-if="row.feeType === 'Percentage'">
                {{ row.feeValue }}%
              </span>
              <span v-else-if="row.feeType === 'FixedAmount'">
                {{ row.feeValue }}
              </span>
              <span v-else>
                无
              </span>
            </template>

            <template #action="{ row }">
              <template v-if="row.username !== 'admin'">
                <el-link type="primary" :underline="false" @click="() => {
                  levelVisible = true;
                  isAdd = false;
                  levelForm = row.level;
                  idForm = row.ruleLevelId;
                  feeTypeForm = row.feeType;
                  feeValueForm = row.feeValue;
                }">
                  修改
                </el-link>
                <el-divider v-if="row.level == form.levels.length" direction="vertical" />
                <el-link v-if="row.level == form.levels.length" type="danger" :underline="false"
                  @click="removeBatch(row)">
                  删除
                </el-link>
              </template>
            </template>
          </ele-data-table>
          <div class="font-12" v-else>暂无规则</div>
        </el-form-item>
        
        <el-form-item label="" prop="description">
          <el-button type="primary" @click="() => {
            levelVisible = true;
            isAdd = true;
            levelForm = form.levels.length + 1;
          }">
            添加规则
          </el-button>
        </el-form-item>

      </el-form>
      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="save">
          保存
        </el-button>
      </template>
    </ele-modal>

    <ele-modal ref="editModal" top="180px" v-model="levelVisible" :width="400" :title="isAdd ? '新增规则' : '修改规则'" @closed="formClose" destroy-on-close>
      <el-form label-width="100px">
        <el-form-item label="层级ID" v-if="idForm">
          <el-input disabled v-model="idForm" placeholder="请输入分销商层级" />
        </el-form-item>
        <el-form-item label="分销商层级">
          <el-input disabled v-model="levelForm" placeholder="请输入分销商层级" />
        </el-form-item>
        <el-form-item label="佣金类型">
          <el-select v-model="feeTypeForm" placeholder="请选择类型" clearable>
            <el-option label="百分比" value="Percentage" />
            <el-option label="固定金额" value="FixedAmount" />
          </el-select>
        </el-form-item>
        <el-form-item label="佣金值">
          <el-input v-model="feeValueForm" placeholder="请输入佣金值" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="add">
          {{ isAdd ? '新增' : '修改' }}
        </el-button>
      </template>
    </ele-modal>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import { requestFeeRuleAdd, requestFeeRuleUpdate, requestFeeRuleDetail } from '@/api/distro/fee-rule/index';


/** 修改回显的数据 */
const props = defineProps({
  data: Object
});

/** 定义事件 */
const emit = defineEmits(['update:modelValue', 'done']);

/** 弹窗是否打开 */
const visible = defineModel('modelValue');

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

let levelVisible = ref(false);
let isAdd = ref(false);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  ruleSetName: null,
  description: null,
  ruleSetId: null,
  levels: [],
});

let levelForm = ref(0);
let idForm = ref(null);
let feeTypeForm = ref(null);
let feeValueForm = ref(null);

/** 表单验证规则 */
const rules = reactive({
  ruleSetName: [
    {
      required: true,
      message: '请输入名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  description: [
    {
      required: false,
      message: '请输入描述',
      type: 'string',
      trigger: 'blur'
    }
  ],
  levels: [
    {
      required: true,
      message: '',
    }
  ]
});

const columns = ref([
  { prop: 'level', label: '分销商层级', align: 'center' },
  { prop: 'feeType', label: '佣金类型', align: 'center', slot: 'feeType', minWidth: 100 },
  { prop: 'feeValue', label: '佣金值', align: 'center', slot: 'feeValue', minWidth: 100 },
  {
    columnKey: 'action',
    label: '操作',
    minWidth: 100,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
])

/** 关闭弹窗 */
const handleCancel = () => {
  emit('update:modelValue', false);
};

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {

    if (!valid) {
      return;
    }

    // 判断是否存在重复的层级
    const levelSet = new Set();
    for (const level of form.levels) {
      if (levelSet.has(level.level)) {
        EleMessage.error('存在重复的分销商层级');
        return;
      }
      levelSet.add(level.level);
    }

    loading.value = true;

    const saveOrUpdate = isUpdate.value ? requestFeeRuleUpdate : requestFeeRuleAdd;

    const params = {
      ...form
    };
    
    saveOrUpdate(params)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

const add = () => {

  // 验证
  if (!feeTypeForm.value) {
    EleMessage.error('请选择佣金类型');
    return;
  }
  if (!feeValueForm.value) {
    EleMessage.error('请输入佣金值');
    return;
  }

  // 数值验证
  if (typeof Number(feeValueForm.value) !== 'number' || Number(feeValueForm.value) < 0) {
    EleMessage.error('佣金值必须为非负数字');
    return;
  }


  if (isAdd.value) {
    form.levels.push({
      level: levelForm.value,
      feeType: feeTypeForm.value,
      feeValue: feeValueForm.value,
    });
  } else {
    // 编辑
    const index = form.levels.findIndex((item) => item.level === levelForm.value);
    form.levels[index].feeType = feeTypeForm.value;
    form.levels[index].feeValue = feeValueForm.value;
  }
  levelVisible.value = false;
}

const formClose = () => {
  // 重置表单
  levelForm.value = 0;
  idForm.value = null;
  feeTypeForm.value = null;
  feeValueForm.value = null;
}

/** 弹窗打开事件 */
const handleOpen = async () => {
  if (props.data) {
    let { ruleSetId } = props.data;
    const result = await requestFeeRuleDetail({ ruleSetId });
    const params = { ...result };
    assignFields(params);
    isUpdate.value = true;
  } else {
    resetFields();
    isUpdate.value = false;
  }
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  });
};

const removeBatch = (row) => {
  form.levels = form.levels.filter((item) => item.level !== row.level);
}
</script>
