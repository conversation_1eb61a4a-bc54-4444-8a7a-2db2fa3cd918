import request from '@/utils/request';
import { handleResponse } from '@/utils/request';
/**
 * 分页查询角色
 */
export async function pageRoles(params) {
  return handleResponse(request.get('/api/system/role/list', { params }));
}

/**
 * 查询角色列表
 */
export async function listRoles(params) {
  return handleResponse(request.get('/api/system/role', { params }));
}

/**
 * 添加角色
 */
export async function addRole(data) {
  return handleResponse(request.post('/api/system/role/add', data));
}

/**
 * 修改角色
 */
export async function updateRole(data) {
  return handleResponse(request.post('/api/system/role/update', data));
}

/**
 * 删除角色
 */
export async function removeRole(id) {
  return handleResponse(request.post('/api/system/role/delete/' + id));
}

/**
 * 批量删除角色
 */
export async function removeRoles(data) {
  return handleResponse(request.post('/api/system/role/delete-batch',  data));
}

/**
 * 获取角色分配的菜单
 */
export async function listRoleMenus() {
  return handleResponse(request.get('/api/system/menu/listByTreeAsRoleSelection'));
}

/**
 * 修改角色菜单
 */
export async function updateRoleMenus(roleId, menuIdList) {
  return handleResponse(request.post('/api/system/role/grant-permissions', {
    roleId,
    menuIdList
  }));
}

/**
 * 获取角色分配的菜单id列表
 */
export async function getRoleMenuIdList(roleId) {
  return handleResponse(request.get('/api/system/role/get-role-menu-id-list?id=' + roleId));
}
