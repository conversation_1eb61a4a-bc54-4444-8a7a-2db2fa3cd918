<template>
  <el-select v-model="selectedValue" @change="handleChange" :placeholder="placeholder" clearable :multiple="multiple"
    :disabled="disabled">
    <el-option v-for="item in roleOptions" :key="item.id" :label="item.roleName" :value="item.id" />
  </el-select>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { pageRoles } from '@/api/system/role'

defineOptions({ name: 'RoleSelect' });

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: undefined
  },
  placeholder: {
    type: String,
    default: '请选择角色'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedValue = ref(props.modelValue)

watch(
  () => props.modelValue,
  (value) => {
    refreshSelectedValue(value)
  }
)

const refreshSelectedValue = (value) => {
  selectedValue.value = undefined
  setTimeout(() => {
    selectedValue.value = value
  }, 0)
}

// 选择事件
const handleChange = (value) => {
  emit('update:modelValue', value)
  emit('change', value)
}

const roleOptions = ref([])

// 获取角色列表
const getRoleList = async () => {
  try {
    const { rows } = await pageRoles()
    roleOptions.value = rows

    refreshSelectedValue(props.modelValue)
  } catch (error) {
    console.error('获取角色列表失败:', error)
  }
}

onMounted(() => {
  getRoleList()
})
</script>
