{"name": "vendor-distro-front", "version": "1.3.0", "type": "module", "private": true, "scripts": {"dev": "vite --host", "serve": "vite build && vite preview --host", "build": "vite build", "serve:staging": "vite build --mode staging && vite preview --host", "build:staging": "vite build --mode staging", "lint:eslint": "eslint --cache --max-warnings 0  \"src/**/*.{vue,js}\" --fix", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite/", "clean:lib": "rimraf node_modules"}, "dependencies": {"@amap/amap-jsapi-loader": "1.0.1", "@ant-design/colors": "7.2.0", "@bytemd/plugin-gfm": "1.21.0", "@bytemd/plugin-highlight": "1.21.0", "@element-plus/icons-vue": "2.3.1", "@vueuse/core": "12.3.0", "axios": "1.7.9", "bytemd": "1.21.0", "countup.js": "2.8.0", "cropperjs": "1.6.2", "crypto-js": "^4.2.0", "dayjs": "1.11.13", "echarts": "5.6.0", "echarts-wordcloud": "2.1.0", "ele-admin-plus": "1.3.0", "element-plus": "2.9.2", "exceljs": "4.4.0", "github-markdown-css": "5.8.1", "highlight.js": "11.11.1", "jsbarcode": "3.11.6", "lodash-es": "4.17.21", "monaco-editor": "0.52.2", "nprogress": "0.2.0", "pinia": "2.3.0", "sortablejs": "1.15.6", "tinymce": "5.10.9", "vue": "3.5.13", "vue-echarts": "7.0.3", "vue-i18n": "11.0.1", "vue-router": "4.5.0", "vuedraggable": "4.1.0", "xgplayer": "3.0.20", "xgplayer-hls": "3.0.20", "xgplayer-music": "3.0.20"}, "devDependencies": {"@vitejs/plugin-vue": "5.2.1", "@vue/compiler-sfc": "3.5.13", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-vue": "9.32.0", "postcss": "8.4.49", "prettier": "3.4.2", "rimraf": "6.0.1", "sass": "1.83.1", "unplugin-vue-components": "28.0.0", "vite": "6.0.7", "vite-plugin-compression": "0.5.1", "vue-eslint-parser": "9.4.3"}, "volta": {"node": "22.12.0"}}