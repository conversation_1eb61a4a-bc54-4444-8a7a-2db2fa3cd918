import request from '@/utils/request';

/**
 * @async
 * @description 查询实体券号段列表 (分页)
 * @param {Object} params - 查询参数
 * @param {String} [params.productId] - 按产品ID筛选 (可选)
 * @param {String} [params.assignedToVendorId] - 按当前持有者分销商ID筛选 (传 'admin' 或 特定值表示查询管理员持有) (可选)
 * @param {String} [params.parentSegmentId] - 按父号段ID筛选 (查询某个号段分配下去的子号段) (可选)
 * @param {String} [params.startNumber] - 按起始券号 (模糊或精确) (可选)
 * @param {String} [params.endNumber] - 按结束券号 (模糊或精确) (可选)
 * @param {Boolean} [params.isActive] - 按是否可用筛选 (可选)
 * @param {Number} [params.page] - 页码 (默认1) (可选)
 * @param {Number} [params.pageSize] - 每页数量 (默认10) (可选)
 */
export async function requestTicketSegmentList(params) {
    const res = await request.get('/api/distro/ticket-segment/list', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 获取实体券号段详细信息
 * @param {Object} params - 查询参数
 * @param {String} params.segmentId - 号段ID (必填)
 */
export async function requestTicketSegmentDetail(params) {
    const res = await request.get('/api/distro/ticket-segment/detail', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 导入/创建顶级实体券号段 (仅管理员)
 * @param {Object} params - 导入参数
 * @param {String} params.productId - 关联的产品ID (必填)
 * @param {String} params.startNumber - 号段起始券号 (必填)
 * @param {String} params.endNumber - 号段结束券号 (必填)
 * @param {String} [params.description] - 批次描述 (可选)
 */
export async function requestTicketSegmentImport(params) {
    const res = await request.post('/api/distro/ticket-segment/add', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 分配实体券号段给下级分销商
 * @param {Object} params - 分配参数
 * @param {String} params.sourceSegmentId - 源号段ID (当前操作者持有) (必填)
 * @param {String} params.targetVendorId - 目标下级分销商ID (必填)
 * @param {String} params.assignStartNumber - 分配起始券号 (必须在源号段范围内且未被分配) (必填)
 * @param {String} params.assignEndNumber - 分配结束券号 (必须在源号段范围内、未被分配、且大于等于起始号) (必填)
 * @param {String} params.description - 分配说明 (可选)
 */
export async function requestTicketSegmentAssign(params) {
    const res = await request.post('/api/distro/ticket-segment/assign', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}