<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="460"
    v-model="visible"
    :title="isUpdate ? '修改样例' : '添加样例'"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="标题" prop="title">
        <el-input
          clearable
          v-model="form.title"
          placeholder="请输入标题"
        />
      </el-form-item>
      <el-form-item label="内容" prop="content">
        <el-input
          clearable
          v-model="form.content"
          placeholder="请输入内容"
        />
      </el-form-item>
      <el-form-item label="上级id" prop="parentId">
        <el-tree-select
          clearable
          check-strictly
          default-expand-all
          :data="treeSelectData"
          node-key="id"
          :props="{ label: 'title' }"
          placeholder="请选择上级id"
          class="ele-fluid"
          v-model="form.parentId"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { EleMessage, toTree } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import { addSample, updateSample, getSampleList } from '@/api/system/sample';

/** 修改回显的数据 */
const props = defineProps({
  data: Object,
  /** 上级id */
  parentId: Number
});

/** 定义事件 */
const emit = defineEmits(['update:modelValue', 'done']);

/** 弹窗是否打开 */
const visible = defineModel('modelValue');

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: null,
  title: null,
  content: null,
  parentId: null
});

/** 表单验证规则 */
const rules = reactive({
});

/** 关闭弹窗 */
const handleCancel = () => {
  emit('update:modelValue', false);
};

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    const saveOrUpdate = isUpdate.value ? updateSample : addSample;
    saveOrUpdate({ ...form, parentId: form.parentId || 0 })
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

/** 弹窗打开事件 */
const handleOpen = () => {
  if (props.data) {
    assignFields({
      ...props.data,
      parentId: props.data.parentId || void 0
    });
    isUpdate.value = true;
  } else {
    resetFields();
    form.parentId = props.parentId;
    isUpdate.value = false;
  }
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  });
};

/** 下拉树数据 */
const treeSelectData = ref([]);

/** 获取下拉树数据 */
getSampleList()
  .then(({rows}) => {
    treeSelectData.value = toTree({
      data: rows || [],
      idField: 'id',
      parentIdField: 'parentId'
    });
  })
  .catch((e) => {
    EleMessage.error(e.message);
  });
</script> 