<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="productId" :columns="columns" :datasource="productList" :pagination="true"
        :show-overflow-tooltip="false">
        <template #toolbar>
          <el-button type="primary" class="ele-btn-icon" :icon="PlusOutlined" @click="openEdit()">
            新建
          </el-button>
        </template>

        <template #isActive="{ row }">
          <el-tag :type="row.isActive ? 'success' : 'danger'">{{ row.isActive ? '已启用' : '未启用' }}</el-tag>
        </template>

        <template #action="{ row }">
          <template v-if="row.username !== 'admin'">
            <el-link v-permission="'system:user:remove'" type="primary" :underline="false" @click="showBatch(row)">
              查看
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <el-link v-permission="'system:user:edit'" type="primary" :underline="false" @click="openEdit(row)">
              修改
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <el-link v-permission="'system:user:remove'" type="danger" :underline="false" @click="removeBatch(row)">
              删除
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <Edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 详情内容 - 抽屉 -->
    <Detail :visible="showDetail" :data="current" @close="onClose" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import {
  PlusOutlined,
} from '@/components/icons';
import { ElMessageBox } from 'element-plus/es';
import { EleMessage } from 'ele-admin-plus/es';
import Search from './components/search.vue';
import Edit from './components/edit.vue';
import Detail from './components/detail.vue';
import { requestProductList, requestProductDelete } from '@/api/distro/product/index';
/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'productId',
    label: '产品ID',
    align: 'center',
    hideInTable: true,
    width: 200,
  },
  {
    prop: 'productName',
    label: '产品名称',
    sortable: 'custom',
    align: 'left',
    minWidth: 160,
  },
  {
    prop: 'productGid',
    label: '产品编号',
    sortable: 'custom',
    align: 'center',
    minWidth: 60
  },
  {
    prop: 'feeRuleSetName',
    label: '分佣规则',
    align: 'center',
    minWidth: 60
  },
  {
    prop: 'isActive',
    label: '是否启用',
    align: 'center',
    minWidth: 60,
    slot: 'isActive',
    filterMultiple: false,
    filters: [
      {
        text: '已启用',
        value: 'true'
      },
      {
        text: '未启用',
        value: 'false'
      }
    ],
  }, 
  {
    prop: 'createTime',
    label: '创建时间',
    sortable: 'custom',
    align: 'center',
    minWidth: 90,
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 180,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 是否显示详情 */
let showDetail = ref(false);

/** 表格数据源 */
const productList = ({ pages, where, orders, filters }) => {
  return requestProductList({ ...where, ...pages, ...orders, ...filters }); // 这里可以替换为你的实际API调
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  // 新增
  showEdit.value = true;
};
/** 删除 */
const removeBatch = (row) => {
  const { productId, productName } = row;
  if (!productId) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '是否确认删除【' + productName + '】的数据项?',
    '系统提示',
    { type: 'warning', draggable: true }
  )
    .then(() => {
      const loading = EleMessage.loading({
        message: '请求中..',
        plain: true
      });

      requestProductDelete({ productId })
        .then(() => {
          loading.close();
          EleMessage.success('删除成功');
          reload();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    })
    .catch(() => { });
};

/** 查看数据 */
const showBatch = (row) => {
  current.value = row;
  // 获取id
  showDetail.value = true;
}

/** 关闭详情 */
const onClose = () => {
  showDetail.value = false;
}
</script>
