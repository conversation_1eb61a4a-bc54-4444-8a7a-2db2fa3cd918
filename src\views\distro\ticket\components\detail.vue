<template>
    <ele-drawer v-model="visible" title="实体券详情" :size="800" style="max-width: 100%;" @open="open" @close="close">        
        <div class="subtitle mt--4"><span>基本信息</span></div>
        <el-descriptions :column="2" border>
            <el-descriptions-item label="实体券ID">
                <ele-copyable>{{ detail.ticketId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="实体券号">
                <ele-copyable>{{ detail.ticketNumber }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="产品ID">
                <ele-copyable>{{ detail.productId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="产品名称">{{ detail.product?.productName }}</el-descriptions-item>
            <el-descriptions-item label="状态">
                <el-tag :type="statusEnum[detail.status]?.type">{{ statusEnum[detail.status]?.label }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="激活时间">{{ detail.activatedTime }}</el-descriptions-item>
            <el-descriptions-item label="分销商ID">
                <ele-copyable v-if="detail.vendorId">{{ detail.vendorId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="分销商">{{ detail.vendor?.name }}</el-descriptions-item>
            <el-descriptions-item label="年卡ID">
                <ele-copyable v-if="detail.linkedCardId">{{ detail.cardId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="年卡卡号">
                <ele-copyable v-if="detail.linkedCardNumber">{{ detail.card?.cardNumber }}</ele-copyable>
            </el-descriptions-item>

            <el-descriptions-item label="用户姓名">{{ detail.linkedCardUserInfo }}</el-descriptions-item>
            <el-descriptions-item label="操作员">{{ detail.updateByUser?.username }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ detail.updateTime }}</el-descriptions-item>
        </el-descriptions>

        <!-- 业务日志表格 -->
        <div class="subtitle mt-2"><span>业务日志</span></div>
        
        <ele-pro-table ref="tableRef" row-key="logId" :toolbar="false" :columns="logColumns"
            :datasource="serviceLogList" :pagination="false" :show-overflow-tooltip="false">
            <template #ticketNumber="{ row }">
                <div v-if="row.ticketNumber">{{ row.ticketNumber }}</div>
                <div v-else class="flex-y">
                    <span>{{ row.startNum }}</span>
                    <span>{{ row.endNum }}</span>
                </div>
            </template>

            <template #serviceType="{ row }">
                <dict-data type="text" code="serviceType" v-model="row.serviceType" />
            </template>

            <template #status="{ row }">
                <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
            </template>
        </ele-pro-table>

    </ele-drawer>
</template>
<script setup>
import { defineProps, defineEmits, computed, reactive, ref } from 'vue';
import { requestTicketDetail } from '@/api/distro/ticket/index';
import { requestServiceLogList } from '@/api/distro/service-log/index';
import { ElMessage } from 'element-plus';

const props = defineProps({
    visible: { type: Boolean, default: false },
    data: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['close'])

const statusEnum = {
    'Available': {
        type: 'success',
        label: '可用'
    },
    'Activated': {
        type: 'warning',
        label: '已激活'
    },
    'Used': {
        type: 'primary',
        label: '已使用'
    }
}

const visible = computed({
    get() { return props.visible },
    set() { }
})

let detail = reactive({
    ticketId: "",
    ticketNumber: "",
    productId: "",
    productName: "",
    currentSegmentId: "",
    currentSegmentRange: "",
    originalVendorChain: [],
    status: "",
    activatedTime: "",
    linkedCardId: "",
    linkedCardNumber: "",
    linkedCardUserInfo: "",
    createBy: "",
    updateBy: "",
    createTime: "",
    updateTime: "",
});

/** 表格实例 */
const tableRef = ref(null);

/** 业务日志表格列配置 */
const logColumns = ref([
    {
        prop: 'ticketNumber',
        label: '实体券',
        align: 'left',
        width: 200,
        slot: 'ticketNumber'
    },
    {
        prop: 'ticketCount',
        label: '数量',
        align: 'center',
        width: 60
    },
    {
        prop: 'cardNumber',
        label: '年卡',
        align: 'center',
        width: 180
    },
    {
        prop: 'serviceType',
        label: '类型',
        align: 'left',
        width: 100,
        slot: 'serviceType'
    },
    {
        prop: 'description',
        label: '描述',
        align: 'left',
        minWidth: 200
    },
    {
        prop: 'remark',
        label: '备注',
        align: 'left',
        minWidth: 60
    },
    {
        prop: 'vendorName',
        label: '分销商',
        align: 'center',
        minWidth: 90
    },
    {
        prop: 'createByUsername',
        label: '操作员',
        align: 'center',
        minWidth: 90
    },
    {
        prop: 'createTime',
        label: '日志时间',
        align: 'center',
        fixed: 'right',
        width: 160
    }
]);

/** 表格数据源 */
const serviceLogList = ({ where }) => {
    return requestServiceLogList({
        ...where,
        ticketNumber: detail.ticketNumber,
        pageSize: 1000,
        pageNo: 1
    });
};

/** 获取状态显示文本 */
const getStatusText = (status) => {
    const statusMap = {
        '0': '正常',
        '1': '异常'
    };
    return statusMap[status] || status;
};

/** 获取状态标签类型 */
const getStatusType = (status) => {
    const typeMap = {
        '0': 'success',
        '1': 'danger'
    };
    return typeMap[status] || 'info';
};

async function open() {
    try {
        // 重置 detail 数据，确保每次打开时数据都是最新的
        Object.assign(detail, {
            ticketId: "",
            ticketNumber: "",
            productId: "",
            productName: "",
            currentSegmentId: "",
            currentSegmentRange: "",
            originalVendorChain: [],
            status: "",
            activatedTime: "",
            linkedCardId: "",
            linkedCardNumber: "",
            linkedCardUserInfo: "",
            createBy: "",
            updateBy: "",
            createTime: "",
            updateTime: "",
        });

        let { ticketId, ticketNumber } = props.data;
        if (!ticketId && !ticketNumber) {
            throw { message: '获取编号失败' }
        }

        // 获取实体券详情
        let result = await requestTicketDetail({ ticketId, ticketNumber });
        Object.assign(detail, result);

        // 刷新业务日志表格
        tableRef.value?.reload?.();
    } catch (error) {
        let content = error.message || '获取数据失败';
        ElMessage.error(content);
    }
}

function close() {
    emit('close');
}
</script>

<style scoped lang="scss">
:deep(.el-descriptions__label) {
    font-weight: bold;
}

.font-14 {
    font-size: 14px;
}

.font-weight-500 {
    font-weight: 500;
}
</style>