import request from '@/utils/request';

/**
 * @async
 * @description 查询分销商列表
 * @param { Object } params
 * @param { String } params.name 分销商名称 (可选, 模糊查询)
 * @param { Number } params.level 分销商层级 (可选)
 * @param { String } params.status 分销商状态 (例如: 'Active', 'Inactive', 可选)
 * @param { String } params.parentVendorId 上级分销商ID (查询直接下级, 可选)
 * @param { Number } params.page 页码 (默认1)
 * @param { Number } params.pageSize 每页数量 (默认10)
 */
export async function requestVendorList(params) {
  const res = await request.get('/api/distro/vendor/list', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 获取分销商详细信息
 * @param { Object } params
 * @param { String } params.vendorId 分销商ID (必填)
 */
export async function requestVendorDetail(params) {
  const res = await request.get('/api/distro/vendor/detail', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 新增分销商 (通常由管理员或有权限的上级执行)
 * @param { Object } params
 * @param { String } params.name 分销商名称 (必填)
 * @param { Number } params.level 上级分销商ID (创建非一级分销商时必填)
 * @param { String } params.contactPerson 联系人 (可选)
 * @param { String } params.phoneNumber 联系电话 (可选)
 * @param { String } params.email 电子邮箱 (可选)
 * @param { String } params.bankAccountInfo 银行账户信息 (JSON格式, 可选)
 * @param { String } params.status 初始状态 (默认'Active', 可选)
 */
export async function requestVendorAdd(params) {
  const res = await request.post('/api/distro/vendor/add', params);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 更新分销商信息
 * @param { Object } params
 * @param { String } params.vendorId 要更新的分销商ID (必填)
 * @param { String } params.name 分销商名称 (可选)
 * @param { String } params.contactPerson 联系人 (可选)
 * @param { String } params.phoneNumber 联系电话 (可选)
 * @param { String } params.email 电子邮箱 (可选)
 * @param { String } params.roleId 重新分配的角色ID (可选)
 * @param { String } params.bankAccountInfo 更新银行账户信息 (JSON格式) (可选)
 * @param { String } params.status 更新状态 (例如: 'Active', 'Inactive', 'Suspended') (可选)
 */
export async function requestVendorUpdate(params) {
  const res = await request.post('/api/distro/vendor/update', params);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/** 
 * @async
 * @description: 删除分销商 (逻辑删除或禁用)
 * @param { Object } params
 * @param { String } params.vendorId 分销商ID (必填)
 */
export async function requestVendorDelete(params) {
  const res = await request.post('/api/distro/vendor/delete', params);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}