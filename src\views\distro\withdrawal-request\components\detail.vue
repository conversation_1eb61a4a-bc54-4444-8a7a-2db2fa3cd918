<template>
    <ele-drawer :model-value="visible" @update:model-value="updateVisible" title="提现申请详情" :size="800"
        style="max-width: 100%;" @open="open" @close="close" destroy-on-close >
        <div v-loading="loading" class="drawer-container">
            <div class="subtitle mt--4"><span>基本信息</span></div>
            <el-descriptions :column="2" border>
            <el-descriptions-item label="申请ID">
                <ele-copyable>{{ detail.requestId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="分销商ID">
                <ele-copyable>{{ detail.vendorId }}</ele-copyable>
            </el-descriptions-item>

            <el-descriptions-item label="申请金额">
                {{ detail.requestedAmount }} 元
            </el-descriptions-item>
            <el-descriptions-item label="申请状态">
                <dict-data type="text" code="withdrawalRequestStatus" v-model="detail.status" />
            </el-descriptions-item>

            <el-descriptions-item label="申请时间">
                {{ detail.requestTime }}
            </el-descriptions-item>
            <el-descriptions-item label="处理时间">
                {{ detail.processingTime }}
            </el-descriptions-item>

            <el-descriptions-item label="完成时间">
                {{ detail.completionTime }}
            </el-descriptions-item>
            <el-descriptions-item label="打款时间">
                {{ detail.paidTime }}
            </el-descriptions-item>

            <el-descriptions-item label="转账备注" :span="2">
                {{ detail.transferRemark }}
            </el-descriptions-item>

            <el-descriptions-item v-if="detail.rejectionReason" label="拒绝理由" :span="2">
                <pre style="color: #F56C6C;">{{ detail.rejectionReason }}</pre>
            </el-descriptions-item>

            <el-descriptions-item label="处理人ID" :span="2">
                <ele-copyable>{{ detail.processedByUserId }}</ele-copyable>
            </el-descriptions-item>

            <el-descriptions-item label="银行账户信息" :span="2">
                {{ detail.bankAccountSnapshot }}
            </el-descriptions-item>
        </el-descriptions>

        <div v-if="detail.transferImageFileId">
            <div class="subtitle mt-2"><span>打款凭证</span></div>
            <el-image :src="detail.transferImageFileId" fit="contain" />
        </div>

        <div class="subtitle mt-2"><span>处理日志</span></div>
        <ele-data-table row-key="logId" :columns="columns" :data="detail.logs || []">
            <template #fromStatus="{ row }">
                <dict-data type="text" code="withdrawalRequestStatus" v-model="row.fromStatus" />
            </template>
            <template #toStatus="{ row }">
                <dict-data type="text" code="withdrawalRequestStatus" v-model="row.toStatus" />
            </template>
        </ele-data-table>
        </div>
    </ele-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import DictData from '@/components/DictData/index.vue';
import { requestWithdrawalRequestDetail } from '@/api/distro/withdrawal-request';

const props = defineProps({
    visible: { type: Boolean, default: false },
    data: { type: Object, default: () => ({}) }
});

const emit = defineEmits(['close', 'update:visible']);

const loading = ref(false);

const detail = reactive({
    requestId: '',
    vendorId: '',
    requestedAmount: '',
    status: '',
    requestTime: '',
    processingTime: '',
    completionTime: '',
    paidTime: '',
    transferRemark: '',
    rejectionReason: '',
    processedByUserId: '',
    bankAccountSnapshot: '',
    transferImageFileId: '',
    logs: []
});

/** 表格列配置 */
const columns = ref([
    {
        prop: 'fromStatus',
        label: '原状态',
        align: 'center',
        width: 120,
        slot: 'fromStatus'
    },
    {
        prop: 'toStatus',
        label: '新状态',
        align: 'center',
        width: 120,
        slot: 'toStatus'
    },
    {
        prop: 'operatorName',
        label: '操作人',
        align: 'center',
        width: 120
    },
    {
        prop: 'operationTime',
        label: '操作时间',
        align: 'center',
        width: 160
    },
    {
        prop: 'remark',
        label: '操作备注',
        align: 'left',
        minWidth: 120
    }
]);

const visible = computed({
    get() { return props.visible },
    set(value) { emit('update:visible', value) }
});

/** 更新visible值 */
const updateVisible = (value) => {
    emit('update:visible', value);
};

/** 加载详情数据 */
async function loadDetail() {
    if (!props.data?.requestId) {
        ElMessage.error('获取申请ID失败');
        return;
    }

    loading.value = true;
    try {
        const result = await requestWithdrawalRequestDetail({
            requestId: props.data.requestId
        });
        Object.assign(detail, result);
    } catch (error) {
        ElMessage.error(error.message || '获取数据失败');
    } finally {
        loading.value = false;
    }
}

/** 打开抽屉时加载数据 */
function open() {
    loadDetail();
}

/** 关闭抽屉 */
function close() {
    emit('close');
}
</script>

<style scoped lang="scss">
.drawer-container {
    padding: 0 12px;
}

:deep(.el-descriptions__label) {
    font-weight: bold;
}
</style>