@use "sass:list";

.w-full {width: 100%;}
.w-4_5 {width: 80%;}
.w-3_4 {width: 75%;}
.w-1_2 {width: 50%;}
.w-1_3 {width: 33%;}
.w-1_4 {width: 25%;}

.h-full {height: 100%;}
.h-1_2 {height: 50%;}
.h-1_3 {height: 33%;}
.h-1_4 {height: 25%;}

.h-100vh{height: 100vh}

.my-tap {opacity: 0.88;background-color: #f1f1f1}

.nowrap {white-space: nowrap}

.hidden {display: none !important;}

.block {display: block !important;}

// color
.color-primary {color: var(--color-primary) !important;}
.color-success {color: var(--color-success) !important;}
.color-warning {color: var(--color-warning) !important;}
.color-danger {color: var(--color-danger) !important;}
.color-info {color: var(--color-info) !important;}

// flex 布局相关
.flex {display: flex !important;}
.flex-x {display: flex; flex-direction: row !important; width: 50/100}
.flex-y {display: flex; flex-direction: column !important;}
.justify-start {justify-content: flex-start;}
.justify-end  {justify-content: flex-end;}
.justify-center {justify-content: center;}
.justify-between {justify-content: space-between;}
.justify-around {justify-content: space-around;}
.justify-evenly {justify-content: space-evenly;}
.items-start {align-items: flex-start;}
.items-end {align-items: flex-end;}
.items-center {align-items: center;}
.items-baseline	{align-items: baseline;}
.items-stretch {align-items: stretch;}
.flex-1 {flex: 1;}
.flex-wrap {flex-wrap: wrap;}
.flex-nowrap {flex-wrap: nowrap;}
.nowrap {white-space: nowrap;}
.flex-center {justify-content: center; align-items: center}

.bg-white {background-color: #fff;}
.bg-danger {background-color: var(--color-danger) !important;}
.bg-success {background-color: var(--color-success) !important;}
.bg-primary {background-color: var(--color-primary) !important;}
.bg-warning {background-color: var(--color-warning) !important;}
.bg-info {background-color: var(--color-info) !important;}

.border-b { border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;} // 下边框,线色
.border-none {border: none !important;}
.subtitle span {
  position: relative;
  font-size: 16px;
  text-align: left;
  padding-left: 9px;
  height: 40px;
  line-height: 40px;
}
.subtitle span::before {
  position: absolute;
  content: "";
  background-color: var(--el-color-primary);
  width: 3px;
  height: 14px;
  left: 0;
  top: 50%;
  margin-top: -7px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}

.font-10 {font-size: 10px !important;}
.font-11 {font-size: 11px !important;}
.font-12 {font-size: 12px !important;}
.font-13 {font-size: 13px !important;}
.font-14 {font-size: 14px !important;}
.font-15 {font-size: 15px !important;}
.font-16 {font-size: 16px !important;}
.font-18 {font-size: 18px !important;}
.font-20 {font-size: 20px !important;}
.font-weight-500 {font-weight: 700!important;}

.m-0 {margin: 0!important;}
.m-5 {margin: 5px!important;}
.m-10 {margin: 10px!important;}
.m-15 {margin: 15px!important;}
.m-20 {margin: 20px!important;}
.m-25 {margin: 25px!important;}
.m-30 {margin: 30px!important;}

.mt-0 {margin-top: 0!important;}
.mt-5 {margin-top: 5px!important;}
.mt-10 {margin-top: 10px!important;}
.mt-15 {margin-top: 15px!important;}
.mt-20 {margin-top: 20px!important;}
.mt-25 {margin-top: 25px!important;}
.mt-30 {margin-top: 30px!important;}

.mr-0 {margin-right: 0!important;}
.mr-5 {margin-right: 5px!important;}
.mr-10 {margin-right: 10px!important;}
.mr-15 {margin-right: 15px!important;}
.mr-20 {margin-right: 20px!important;}
.mr-25 {margin-right: 25px!important;}
.mr-30 {margin-right: 30px!important;}

.mb-0 {margin-bottom: 0!important;}
.mb-5 {margin-bottom: 5px!important;}
.mb-10 {margin-bottom: 10px!important;}
.mb-15 {margin-bottom: 15px!important;}
.mb-20 {margin-bottom: 20px!important;}
.mb-25 {margin-bottom: 25px!important;}
.mb-30 {margin-bottom: 30px!important;}

.ml-0 {margin-left: 0!important;}
.ml-5 {margin-left: 5px!important;}
.ml-10 {margin-left: 10px!important;}
.ml-15 {margin-left: 15px!important;}
.ml-20 {margin-left: 20px!important;}
.ml-25 {margin-left: 25px!important;}
.ml-30 {margin-left: 30px!important;}

.p-0 {padding: 0!important;}
.p-5 {padding: 5px!important;}
.p-10 {padding: 10px!important;}
.p-15 {padding: 15px!important;}
.p-20 {padding: 20px!important;}
.p-25 {padding: 25px!important;}
.p-30 {padding: 30px!important;}

.pt-0 {padding-top: 0!important;}
.pt-5 {padding-top: 5px!important;}
.pt-10 {padding-top: 10px!important;}
.pt-15 {padding-top: 15px!important;}
.pt-20 {padding-top: 20px!important;}
.pt-25 {padding-top: 25px!important;}
.pt-30 {padding-top: 30px!important;}

.pr-0 {padding-right: 0!important;}
.pr-5 {padding-right: 5px!important;}
.pr-10 {padding-right: 10px!important;}
.pr-15 {padding-right: 15px!important;}
.pr-20 {padding-right: 20px!important;}
.pr-25 {padding-right: 25px!important;}
.pr-30 {padding-right: 30px!important;}

.pb-0 {padding-bottom: 0!important;}
.pb-5 {padding-bottom: 5px!important;}
.pb-10 {padding-bottom: 10px!important;}
.pb-15 {padding-bottom: 15px!important;}
.pb-20 {padding-bottom: 20px!important;}
.pb-25 {padding-bottom: 25px!important;}
.pb-30 {padding-bottom: 30px!important;}

.pl-0 {padding-left: 0!important;}
.pl-5 {padding-left: 5px!important;}
.pl-10 {padding-left: 10px!important;}
.pl-15 {padding-left: 15px!important;}
.pl-20 {padding-left: 20px!important;}
.pl-25 {padding-left: 25px!important;}
.pl-30 {padding-left: 30px!important;}

.f-l{
  float: left;
}

.f-r{
  float: right;
}


.text-ellipsis {text-overflow: ellipsis; white-space: nowrap; overflow: hidden;}

// 生成单个方向space的混合
@mixin space-single-direction-generator($property, $direction, $size) {
    #{$property}-#{$direction}: (0.25em * $size) !important;
}

// 生成不同尺寸、不同方向的padding类名
@mixin generate-space-classes($simpleProperty, $property) {
  // 定义方向相关信息
  $directions: t, r, b, l, x, y;  // 方向缩写列表
  // 对应的CSS属性列表，按照顺序与方向缩写对应
  $direction-properties: top, right, bottom, left;

  @for $index from -16 through 16 {
    @for $i from 1 through 4 {
      $direction-abbr: list.nth($directions, $i);
      $direction-property: list.nth($direction-properties, $i);

      .#{$simpleProperty}#{$direction-abbr}-#{$index} {
        @include space-single-direction-generator($property, $direction-property, $index);
      }
    }

    .#{$simpleProperty}x-#{$index} {
      @include space-single-direction-generator($property, left, $index);
      @include space-single-direction-generator($property, right, $index);
    }
    .#{$simpleProperty}y-#{$index} {
      @include space-single-direction-generator($property, top, $index);
      @include space-single-direction-generator($property, bottom, $index);
    }
  }
}

// 调用函数来生成padding、margin类
@include generate-space-classes(m, margin);
@include generate-space-classes(p, padding);

// 生成 gap1-16 的类名
@mixin generate-gap-classes($max: 16) {
  @for $index from 1 through $max {
    .gap-#{$index} {
      gap: (0.25em * $index);
    }
  }
}

@include generate-gap-classes();

.vxe-body--column {
  .el-switch__label * {
    font-size: 12px;
  }
}

/* 为table的操作列设置 outline 样式 */
.table-actions {
  display: flex;
  gap: 6px;
  justify-content: center;
  flex-wrap: wrap;
  
  .divide {
    color: var(--color-info);
  }
  
  .el-badge__content {
    height: 14px;
    line-height: 14px;
    font-size: 10px;
    margin-top: 1px;
    margin-left: 2px;
  }

  .el-button--primary.is-plain {
    background-color: #fff;
    color: var(--color-primary);
  }
  
  .el-button--success.is-plain {
    background-color: #fff;
    color: var(--color-success);
  }
  
  .el-button--danger.is-plain {
    background-color: #fff;
    color: var(--color-danger);
  }
  
  .el-button--info.is-plain {
    background-color: #fff;
    color: var(--color-info);
  }
  
  .el-button--warning.is-plain {
    background-color: #fff;
    color: var(--color-warning);
  }

  .el-button--primary.is-plain {
    &:hover, &:active, &:focus {
      background-color: var(--color-primary);
      color: #fff;
    }
  }
  
  .el-button--success.is-plain {
    &:hover, &:active, &:focus {
      background-color: var(--color-success);
      color: #fff;
    }
  }
  
  .el-button--danger.is-plain {
    &:hover, &:active, &:focus {
      background-color: var(--color-danger);
      color: #fff;
    }
  }
  
  .el-button--info.is-plain {
    &:hover, &:active, &:focus {
      background-color: var(--color-info);
      color: #fff;
    }
  }
  
  .el-button--warning.is-plain {
    &:hover, &:active, &:focus {
      background-color: var(--color-warning);
      color: #fff;
    }
  }

  button {
    margin-left: 0px !important;
    padding: 6px 8px !important;

    span {
      margin-left: 0px !important;
    }
  }
} 