<!-- 编辑弹窗 -->
<template>
  <ele-modal form :width="560" v-model="visible" :title="isUpdate ? '修改年卡产品信息' : '新增年卡产品'" @open="handleOpen"
    destroy-on-close>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" @submit.prevent="">
      <el-form-item label="产品名称" prop="productName">
        <el-input clearable v-model="form.productName" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item v-if="isUpdate" label="产品ID" prop="productId">
        <el-input clearable v-model="form.productId" disabled placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="产品编号" prop="productGid">
        <el-input clearable v-model="form.productGid" placeholder="请输入产品编号" />
      </el-form-item>
      <el-form-item label="产品描述" prop="description">
        <el-input clearable v-model="form.description" resize="none" type="textarea" :rows="5" placeholder="请输入描述" />
      </el-form-item>
      <el-form-item label="是否启用" prop="isActive">
        <el-switch v-model="form.isActive" :active-value="true" :inactive-value="false" inline-prompt active-text="是"
          inactive-text="否" />
      </el-form-item>
      <el-form-item label="关联规则集ID" prop="feeRuleSetId">
        <!-- <el-input clearable v-model="form.feeRuleSetId" placeholder="请输入关联ID" /> -->
        <el-select v-model="form.feeRuleSetId" filterable placeholder="请选择关联ID">
          <el-option v-for="item in rulesetList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import { requestProductAdd, requestProductUpdate, requestProductDetail } from '@/api/distro/product/index';
import { requestFeeRuleList } from '@/api/distro/fee-rule/index';

/** 修改回显的数据 */
const props = defineProps({
  data: Object
});

/** 定义事件 */
const emit = defineEmits(['update:modelValue', 'done']);

/** 弹窗是否打开 */
const visible = defineModel('modelValue');

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 规则集 id 列表 */
let rulesetList = ref([]); // 初始化为空数组，后续会被填充数据

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  productGid: null,
  productName: null,
  productId: null,
  description: null,
  feeRuleSetId: null,
  isActive: false,
});

/** 表单验证规则 */
const rules = reactive({
  productName: [
    {
      required: true,
      message: '请输入产品名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  productGid: [
    {
      required: true,
      message: '请输入Gid',
      type: 'string',
      trigger: 'blur'
    }
  ],
  realName: [
    {
      required: false,
      type: 'string',
      trigger: 'blur'
    }
  ],
  feeRuleSetId: [
    {
      required: false,
      type: 'string',
      trigger: 'blur'
    }
  ],
});

/** 关闭弹窗 */
const handleCancel = () => {
  emit('update:modelValue', false);
};

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;

    const saveOrUpdate = isUpdate.value ? requestProductUpdate : requestProductAdd;
    // 如果有密码字段且不为空，则加密
    const params = {
      ...form
    };

    saveOrUpdate(params)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

/** 弹窗打开事件 */
const handleOpen = async () => {
  // 获取关联的规则集ID 列表
  const result = await requestFeeRuleList({})
  rulesetList.value = result.rows.map(child => {
    return {
      label: child.ruleSetName, // 显示的标签
      value: child.ruleSetId, // 实际的值
    };
  });

  if (props.data) {
    const data = Object.assign({}, props.data);

    // 请求详情
    let result = await requestProductDetail({productId: data.productId})
    
    assignFields(result);
    isUpdate.value = true;
  } else {
    resetFields();
    isUpdate.value = false;
  }
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  });
};
</script>
