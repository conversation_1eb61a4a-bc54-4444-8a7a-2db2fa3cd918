<template>
  <ele-page>
    <!-- 搜索表单 -->
    <role-search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
      ref="tableRef" row-key="id" :columns="columns" :datasource="datasource"
        :show-overflow-tooltip="true" v-model:selections="selections" :highlight-current-row="true"
        :export-config="{ fileName: '角色数据', datasource: exportSource }" :print-config="{ datasource: exportSource }"
        cache-key="systemRoleTable">
        <template #toolbar>
          <el-button type="primary" class="ele-btn-icon" :icon="PlusOutlined" @click="openEdit()">
            新建
          </el-button>
          <el-button type="danger" class="ele-btn-icon" :icon="DeleteOutlined" @click="remove()">
            删除
          </el-button>
        </template>

        <template #dataScope="{ row }">
          <dict-data code="DataScope" :model-value="row.dataScope" type="text"/>
        </template>

        <template #action="{ row }">
          <template v-if="row.id !== '13001'">
            <el-link type="primary" :underline="false" @click="openEdit(row)">
              修改
            </el-link>
          <el-divider direction="vertical" />
          <el-link type="danger" :underline="false" @click="remove(row)">
            删除
          </el-link>
          <el-divider direction="vertical" />
          <el-dropdown class="ele-dropdown-trigger" :style="{ display: 'inline' }">
            <el-link type="primary" :underline="false">
              更多
              <el-icon>
                <IconElArrowDown />
              </el-icon>
            </el-link>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="openMenuAuth(row)">菜单权限</el-dropdown-item>
              </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <role-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 菜单权限分配弹窗 -->
    <role-menu-auth v-model="showMenuAuth" :data="current" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessageBox } from 'element-plus/es';
import { EleMessage } from 'ele-admin-plus/es';
import { PlusOutlined, DeleteOutlined } from '@/components/icons';
import RoleSearch from './components/role-search.vue';
import RoleEdit from './components/role-edit.vue';
import RoleMenuAuth from './components/role-menu-auth.vue';
import { pageRoles, removeRoles, listRoles } from '@/api/system/role';

defineOptions({ name: 'SystemRole' });

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 50,
    align: 'center',
    fixed: 'left'
  },
  {
    type: 'index',
    columnKey: 'index',
    width: 50,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'roleName',
    label: '角色名称',
    minWidth: 120
  },
  {
    prop: 'roleKey',
    label: '角色标识',
    minWidth: 120
  },
  {
    prop: 'weight',
    label: '权重',
    sortable: 'custom',
    width: 100
  },
  {
    prop: 'dataScope',
    label: '数据权限',
    slot: 'dataScope',
    minWidth: 120
  },
  {
    prop: 'remark',
    label: '备注',
    minWidth: 140
  },
  {
    prop: 'createTime',
    label: '创建时间',
    sortable: 'custom',
    width: 180,
    align: 'center'
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 200,
    align: 'center',
    slot: 'action',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 是否显示菜单权限分配弹窗 */
const showMenuAuth = ref(false);

/** 表格数据源 */
const datasource = ({ pages, where, orders }) => {
  return pageRoles({ ...where, ...orders, ...pages });
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  tableRef.value?.reload?.({ page: 1, where });
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 打开菜单权限分配弹窗 */
const openMenuAuth = (row) => {
  current.value = row ?? null;
  showMenuAuth.value = true;
};

/** 删除 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.roleName).join(', ') + '”吗?',
    '系统提示',
    { type: 'warning', draggable: true }
  )
    .then(() => {
      const loading = EleMessage.loading({
        message: '请求中..',
        plain: true
      });
      removeRoles(rows.map((d) => d.id))
        .then((msg) => {
          loading.close();
          EleMessage.success(msg);
          reload();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    })
    .catch(() => { });
};

/** 导出和打印全部数据的数据源 */
const exportSource = ({ where, orders }) => {
  return listRoles({ ...where, ...orders });
};
</script>
