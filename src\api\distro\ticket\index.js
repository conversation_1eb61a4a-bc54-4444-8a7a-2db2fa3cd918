import request from '@/utils/request';


/**
 * @async
 * @description 查询实体券列表 (分页)
 * @param {Object} params - 查询参数
 * @param {String} [params.ticketNumber] - 按实体券券号精确查询 (可选)  
 * @param {String} [params.productId] - 按产品ID筛选 (可选)
 * @param {String} [params.currentSegmentId] - 按当前所属号段ID筛选 (可选)
 * @param {String} [params.status] - 按券状态筛选 ('Available', 'Activated', 'Used') (可选)
 * @param {String} [params.activationTimeStart] - 激活时间范围起始 (格式: yyyy-MM-dd HH:mm:ss) (可选)
 * @param {String} [params.activationTimeEnd] - 激活时间范围结束 (格式: yyyy-MM-dd HH:mm:ss) (可选)
 * @param {String} [params.vendorId] - 查询由该分销商（及其下级）售出/激活的券 (需要基于`original_vendor_chain`判断) (可选)
 * @param {Number} [params.page] - 页码 (默认1) (可选)
 * @param {Number} [params.pageSize] - 每页数量 (默认10) (可选)
 */
export async function requestTicketList(params) {
    const res = await request.get('/api/distro/ticket/list', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 获取实体券详细信息
 * @param {Object} params - 查询参数
 * @param {String} [params.ticketId] - 券ID (优先使用) (可选)
 * @param {String} [params.ticketNumber] - 券号 (如果ID未知) (可选)
 */
export async function requestTicketDetail(params) {
    const res = await request.get('/api/distro/ticket/detail', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 分配实体券
 * @param {Object} data - 请求参数
 * @param {String} data.targetVendorId - 分配目标分销商ID
 * @param {String} data.ticketNumber - 券号，多个用逗号分隔
 * @param {Number} ticketCount - 预期分配券的数量
 * @param {String} [data.startNum] - 起始券号（批量分配用）
 * @param {String} [data.endNum] - 结束券号（批量分配用）
 * @param {String} [data.remark] - 备注信息 (可选)
 */
export async function requestAssignTicket(data) {
    const res = await request.post('/api/distro/ticket/assign', data);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 回收实体券
 * @param {Object} data - 请求参数
 * @param {String} data.ticketNumber - 券号，多个用逗号分隔
 * @param {Number} ticketCount - 预期回收券的数量
 * @param {String} [data.startNum] - 起始券号（批量回收用）
 * @param {String} [data.endNum] - 结束券号（批量回收用）
 * @param {String} [data.remark] - 备注信息 (可选)
 */
export async function requestCancelAssignTicket(data) {
    const res = await request.post('/api/distro/ticket/cancel-assign', data);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}