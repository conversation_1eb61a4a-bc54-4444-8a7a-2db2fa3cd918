import request from '@/utils/request';

/**
 * @async
 * @description 查询佣金明细列表 (分页)
 * @param {Object} params - 查询参数
 * @param {String} [params.ledgerId] - 按总账ID查询 (可选)
 * @param {String} [params.renewalTimeStart] - 佣金产生时间范围起始 (可选)
 * @param {String} [params.renewalTimeEnd] - 佣金产生时间范围结束 (可选)
 * @param {String} [params.settleTimeStart] - 结算到余额时间范围起始 (可选)
 * @param {String} [params.settleTimeEnd] - 结算到余额时间范围结束 (可选)
 * @param {String} [params.status] - 按佣金状态筛选 ('PendingSettlement', 'Settled', 'IncludedInWithdrawal') (可选)
 * @param {String} [params.ticketNumber] - 按产生佣金的实体券号查询 (可选)
 * @param {String} [params.cardNumber] - 按年卡号查询 (可选)
 * @param {String} [params.vendorId] - 按分销商ID查询 (可选)
 * @param {String} [params.productId] - 按产品ID查询 (可选)
 * @param {String} [params.renewalId] - 按续期记录ID查询 (可选)
 * @param {Number} [params.page] - 页码 (默认1) (可选)
 * @param {Number} [params.pageSize] - 每页数量 (默认10) (可选)
 */
export async function requestFeeDistroList(params) {
  const res = await request.get('/api/distro/fee-distro/list', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

