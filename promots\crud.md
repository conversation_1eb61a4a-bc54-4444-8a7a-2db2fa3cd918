# 单表增删改查
## 变量
模块：{moduleName}
功能：{functionCode} {functionName}
以角色管理为例，模块：system，功能：role 角色

## 生成crud代码
1. 复制 `src/views/system/role` 目录及其子目录下的页面实现，不需要 role-auth部分
2. 复制 `src/api/system/role.js`
3. 修改接口和字段信息

# 树表增删改查
## 变量
模块：{moduleName}
功能：{functionCode} {functionName}
以团队管理为例，模块：course，功能：team 团队

## 生成crud代码
1. 复制 `src/views/course/team` 目录及其子目录下的页面实现，不需要 role-auth部分
2. 复制 `src/api/course/team.js`
3. 修改接口和字段信息
