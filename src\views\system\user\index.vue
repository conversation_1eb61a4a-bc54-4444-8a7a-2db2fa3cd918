<template>
  <ele-page>
    <!-- 搜索表单 -->
    <user-search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="id" :columns="columns" :datasource="datasource"
        :show-overflow-tooltip="true" v-model:selections="selections" :highlight-current-row="true"
        :export-config="{ fileName: '系统用户' }" cache-key="systemUserTable">
        <template #toolbar>
          <el-button v-permission="'system:user:add'" type="primary" class="ele-btn-icon" :icon="PlusOutlined"
            @click="openEdit()">
            新建
          </el-button>
          <el-button v-permission="'system:user:remove'" type="danger" class="ele-btn-icon" :icon="DeleteOutlined"
            @click="removeBatch()">
            删除
          </el-button>
          <el-button v-permission="'system:user:export'" class="ele-btn-icon" :icon="DownloadOutlined"
            @click="exportData">
            导出
          </el-button>
        </template>

        <template #sex="{ row }">
          <el-tag v-if="row.sex === 1" type="success">男</el-tag>
          <el-tag v-else-if="row.sex === 0" type="danger">女</el-tag>
          <el-tag v-else type="warning">未知</el-tag>
        </template>

        <template #accountStatus="{ row }">
          <dict-data code="accountStatus" :model-value="row.accountStatus" type="text" />
        </template>

        <template #roleList="{ row }">
          <el-tag v-for="role in row.roleList" size="small" :key="role.id" type="info" class="mr-2">{{ role.roleName }}</el-tag>
        </template>

        <template #teamList="{ row }">
          <el-tag v-for="team in row.teamList" size="small" :key="team.id" type="info" class="mr-2">{{ team.teamNameWithParent }}</el-tag>
        </template>

        <template #action="{ row }">
          <template v-if="row.username !== 'admin'">
            <el-link v-permission="'system:user:edit'" type="primary" :underline="false" @click="openEdit(row)">
              修改
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <el-link v-permission="'system:user:remove'" type="danger" :underline="false" @click="removeBatch(row)">
              删除
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <ele-dropdown v-if="moreItems.length" :items="moreItems" style="display: inline"
              @command="(key) => dropClick(key, row)">
              <el-link type="primary" :underline="false">
                <span>更多</span>
                <el-icon :size="12" style="vertical-align: -1px; margin-left: 2px">
                  <IconElArrowDown />
                </el-icon>
              </el-link>
            </ele-dropdown>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <user-edit v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
import { ref, computed } from 'vue';
import {
  PlusOutlined,
  DeleteOutlined,
  DownloadOutlined
} from '@/components/icons';
import { ElMessageBox } from 'element-plus/es';
import { EleMessage } from 'ele-admin-plus/es';
import UserSearch from './components/user-search.vue';
import UserEdit from './components/user-edit.vue';
import { pageUsers, removeUsers, exportUsers, resetUserPassword } from '@/api/system/user';

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 50,
    align: 'center',
    fixed: 'left'
  },
  {
    type: 'index',
    columnKey: 'index',
    width: 50,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'username',
    label: '用户名',
    align: 'center',
    minWidth: 110
  },
  {
    prop: 'roleList',
    label: '角色',
    align: 'center',
    minWidth: 200,
    slot: 'roleList'
  },
  {
    prop: 'realName',
    label: '姓名',
    align: 'center',
  },
  {
    prop: 'sex',
    label: '性别',
    align: 'center',
    slot: 'sex',
  },
  {
    prop: 'mobilePhone',
    label: '手机号',
    align: 'center',
    minWidth: 110
  },
  {
    prop: 'accountStatus',
    label: '状态',
    align: 'center',
    minWidth: 60,
    slot: 'accountStatus'
  },
  {
    prop: 'avatar',
    label: '头像地址',
    align: 'center',
    hideInTable: true,
    minWidth: 110
  },
  {
    prop: 'lastLoginIp',
    label: '最后登录ip',
    align: 'center',
    minWidth: 110,
    hideInTable: true
  },
  {
    prop: 'vendorName',
    label: '分销商名称',
    align: 'center',
    minWidth: 110
  },
  {
    prop: 'lastLoginTime',
    label: '最后登录',
    align: 'center',
    minWidth: 110
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 180,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({ pages, where }) => {
  return pageUsers({ ...where, ...pages });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 更多操作 */
const moreItems = computed(() => {
  return [
    { title: '重置密码', command: 'resetPassword' }
  ];
});
/** 更多操作 */
const dropClick = (key, row) => {
  if (key === 'resetPassword') {
    ElMessageBox.confirm('是否确认重置密码【' + row.username + '】?', '系统提示', { type: 'warning', draggable: true })
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        resetUserPassword(row.id)
          .then(() => {
            EleMessage.success('密码已重置为123456');
          })
          .catch((e) => {
            EleMessage.error(e.message);
          }).finally(() => {
            loading.close();
          });
      });
  }
};


/** 批量删除 */
const removeBatch = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '是否确认删除【' + rows.map(r => r.username).join(',') + '】的数据项?',
    '系统提示',
    { type: 'warning', draggable: true }
  )
    .then(() => {
      const loading = EleMessage.loading({
        message: '请求中..',
        plain: true
      });
      removeUsers(rows.map((d) => d.id))
        .then(() => {
          loading.close();
          EleMessage.success('删除成功');
          reload();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    })
    .catch(() => { });
};

/** 导出数据 */
const exportData = () => {
  const loading = EleMessage.loading({
    message: '请求中..',
    plain: true
  });
  tableRef.value?.fetch?.(({ where }) => {
    exportUsers(where)
      .then(() => {
        loading.close();
      })
      .catch((e) => {
        loading.close();
        EleMessage.error(e.message);
      });
  });
};
</script>
