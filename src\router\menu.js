export const STATIC_MENU = [
  // {
  //   path: '/dashboard',
  //   component: null,
  //   meta: {
  //     title: 'Dashboard',
  //     icon: 'IconProHomeOutlined',
  //     hide: false
  //   },
  //   redirect: '/dashboard/workplace',
  //   children: [
  //     {
  //       path: '/dashboard/workplace',
  //       component: '/dashboard/workplace',
  //       meta: {
  //         title: '工作台',
  //         icon: 'IconProDesktopOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '工作臺',
  //           en: 'Workplace'
  //         }
  //       }
  //     },
  //     {
  //       path: '/dashboard/analysis',
  //       component: '/dashboard/analysis',
  //       meta: {
  //         title: '分析页',
  //         icon: 'IconProAnalysisOutlined',
  //         hide: false,
  //         props: {
  //           badge: {
  //             value: 1,
  //             type: 'warning'
  //           }
  //         },
  //         lang: {
  //           zh_TW: '分析頁',
  //           en: 'Analysis'
  //         }
  //       }
  //     },
  //     {
  //       path: '/dashboard/monitor',
  //       component: '/dashboard/monitor',
  //       meta: {
  //         title: '监控页',
  //         icon: 'IconProDashboardOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '監控頁',
  //           en: 'Monitor'
  //         }
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/system',
  //   meta: {
  //     title: '系统管理',
  //     icon: 'IconProSettingOutlined',
  //     hide: false,
  //     lang: {
  //       zh_TW: '系統管理',
  //       en: 'System'
  //     }
  //   },
  //   redirect: '/system/user',
  //   children: [
  //     {
  //       path: '/system/user',
  //       component: '/system/user',
  //       meta: {
  //         title: '用户管理',
  //         icon: 'IconProUserOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '用戶管理',
  //           en: 'User'
  //         }
  //       },
  //       children: [
  //         {
  //           path: '/system/user/details/:id',
  //           component: '/system/user/details',
  //           meta: {
  //             title: '用户详情',
  //             icon: 'IconProUserOutlined',
  //             hide: true,
  //             active: '/system/user',
  //             lang: {
  //               zh_TW: '用戶詳情',
  //               en: 'UserDetails'
  //             }
  //           }
  //         }
  //       ]
  //     },
  //     {
  //       path: '/system/role',
  //       component: '/system/role',
  //       meta: {
  //         title: '角色管理',
  //         icon: 'IconProIdcardOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '角色管理',
  //           en: 'Role'
  //         }
  //       }
  //     },
  //     {
  //       path: '/system/sample',
  //       component: '/system/sample',
  //       meta: {
  //         title: '示例管理',
  //         icon: 'IconProAppstoreOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '示例管理',
  //           en: 'Sample'
  //         }
  //       }
  //     },
  //     {
  //       path: '/system/menu',
  //       component: '/system/menu',
  //       meta: {
  //         title: '菜单管理',
  //         icon: 'IconProAppstoreOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '選單管理',
  //           en: 'Menu'
  //         }
  //       }
  //     },
  //     {
  //       path: '/system/organization',
  //       component: '/system/organization',
  //       meta: {
  //         title: '机构管理',
  //         icon: 'IconProCityOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '機构管理',
  //           en: 'Organization'
  //         }
  //       }
  //     },
  //     {
  //       path: '/system/dict',
  //       component: '/system/dict',
  //       meta: {
  //         title: '字典管理',
  //         icon: 'IconProBookOutlined',
  //         hide: false,
  //         hideFooter: true,
  //         lang: {
  //           zh_TW: '字典管理',
  //           en: 'Dictionary'
  //         }
  //       }
  //     },
  //     {
  //       path: '/system/file',
  //       component: '/system/file',
  //       meta: {
  //         title: '文件管理',
  //         icon: 'IconProFolderOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '檔案管理',
  //           en: 'File'
  //         }
  //       }
  //     },
  //     {
  //       path: '/system/login-record',
  //       component: '/system/login-record',
  //       meta: {
  //         title: '登录日志',
  //         icon: 'IconProCalendarOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '登入日誌',
  //           en: 'LoginRecord'
  //         }
  //       }
  //     },
  //     {
  //       path: '/system/operation-record',
  //       component: '/system/operation-record',
  //       meta: {
  //         title: '操作日志',
  //         icon: 'IconProLogOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '操作日誌',
  //           en: 'OperationRecord'
  //         }
  //       }
  //     },
  //     {
  //       path: '/system/gen',
  //       component: '/system/gen',
  //       meta: {
  //         title: '代码生成',
  //         icon: 'IconProCodeOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '代码生成',
  //           en: 'GenCode'
  //         }
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/form',
  //   component: null,
  //   meta: {
  //     title: '表单页面',
  //     icon: 'IconProFormOutlined',
  //     hide: false,
  //     props: {
  //       badge: {
  //         value: 'New'
  //       }
  //     },
  //     lang: {
  //       zh_TW: '表單頁面',
  //       en: 'Form'
  //     }
  //   },
  //   redirect: '/form/basic',
  //   children: [
  //     {
  //       path: '/form/basic',
  //       component: '/form/basic',
  //       meta: {
  //         title: '基础表单',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '基礎表單',
  //           en: 'Basic Form'
  //         }
  //       }
  //     },
  //     {
  //       path: '/form/advanced',
  //       component: '/form/advanced',
  //       meta: {
  //         title: '复杂表单',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '複雜表單',
  //           en: 'Advanced Form'
  //         }
  //       }
  //     },
  //     {
  //       path: '/form/step',
  //       component: '/form/step',
  //       meta: {
  //         title: '分步表单',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '分步表單',
  //           en: 'Step Form'
  //         }
  //       }
  //     },
  //     {
  //       path: '/form/build',
  //       component: '/form/build',
  //       meta: {
  //         title: '表单构建',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         props: {
  //           badge: {
  //             isDot: true
  //           }
  //         },
  //         lang: {
  //           zh_TW: '表單構建',
  //           en: 'Form Build'
  //         }
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/list',
  //   component: null,
  //   meta: {
  //     title: '列表页面',
  //     icon: 'IconProTableOutlined',
  //     hide: false,
  //     props: {
  //       hideTimeout: 450
  //     },
  //     lang: {
  //       zh_TW: '清單頁面',
  //       en: 'List'
  //     }
  //   },
  //   redirect: '/list/basic',
  //   children: [
  //     {
  //       path: '/list/basic',
  //       component: '/list/basic',
  //       meta: {
  //         title: '基础列表',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '基礎清單',
  //           en: 'Basic List'
  //         }
  //       },
  //       children: [
  //         {
  //           path: '/list/basic/edit/:id',
  //           component: '/list/basic/edit',
  //           meta: {
  //             title: '修改用户',
  //             icon: 'IconProLinkOutlined',
  //             hide: true,
  //             active: '/list/basic',
  //             lang: {
  //               zh_TW: '編輯用戶',
  //               en: 'Edit User'
  //             }
  //           }
  //         },
  //         {
  //           path: '/list/basic/add',
  //           component: '/list/basic/add',
  //           meta: {
  //             title: '添加用户',
  //             icon: 'IconProLinkOutlined',
  //             hide: true,
  //             active: '/list/basic',
  //             lang: {
  //               zh_TW: '添加用戶',
  //               en: 'Add User'
  //             }
  //           }
  //         }
  //       ]
  //     },
  //     {
  //       path: '/list/user',
  //       component: '/list/user',
  //       meta: {
  //         title: '左树右表',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '左樹右表',
  //           en: 'Tree List'
  //         }
  //       }
  //     },
  //     {
  //       path: '/list/advanced',
  //       component: '/list/advanced',
  //       meta: {
  //         title: '复杂列表',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '複雜清單',
  //           en: 'Advanced List'
  //         }
  //       }
  //     },
  //     {
  //       path: '/list/card',
  //       component: '/list/card',
  //       meta: {
  //         title: '卡片列表',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         props: {
  //           hideTimeout: 100
  //         },
  //         lang: {
  //           zh_TW: '卡片清單',
  //           en: 'Card List'
  //         }
  //       },
  //       redirect: '/list/card/project',
  //       children: [
  //         {
  //           path: '/list/card/project',
  //           component: '/list/card/project',
  //           meta: {
  //             title: '项目列表',
  //             icon: 'IconProLinkOutlined',
  //             hide: false,
  //             lang: {
  //               zh_TW: '項目清單',
  //               en: 'Project'
  //             }
  //           }
  //         },
  //         {
  //           path: '/list/card/application',
  //           component: '/list/card/application',
  //           meta: {
  //             title: '应用列表',
  //             icon: 'IconProLinkOutlined',
  //             hide: false,
  //             lang: {
  //               zh_TW: '應用清單',
  //               en: 'Application'
  //             }
  //           }
  //         },
  //         {
  //           path: '/list/card/article',
  //           component: '/list/card/article',
  //           meta: {
  //             title: '文章列表',
  //             icon: 'IconProLinkOutlined',
  //             hide: false,
  //             lang: {
  //               zh_TW: '文章清單',
  //               en: 'Article'
  //             }
  //           }
  //         }
  //       ]
  //     },
  //     {
  //       path: '/list/users',
  //       component: null,
  //       meta: {
  //         title: '复杂路由',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         props: {
  //           hideTimeout: 100
  //         },
  //         lang: {
  //           zh_TW: '複雜路由',
  //           en: 'Route Demo'
  //         }
  //       },
  //       redirect: '/list/users/1',
  //       children: [
  //         {
  //           path: '/list/users/1',
  //           component: '/list/users',
  //           meta: {
  //             title: '男用户',
  //             icon: 'IconProLinkOutlined',
  //             hide: false,
  //             lang: {
  //               zh_TW: '男用戶',
  //               en: 'Male Users'
  //             }
  //           },
  //           children: [
  //             {
  //               path: '/list/users/details/1/:id',
  //               component: '/list/users/details',
  //               meta: {
  //                 title: '男用户详情',
  //                 icon: 'IconProLinkOutlined',
  //                 hide: true,
  //                 active: '/list/users/1',
  //                 lang: {
  //                   zh_TW: '男用戶詳情',
  //                   en: 'MaleUserDetails'
  //                 }
  //               }
  //             }
  //           ]
  //         },
  //         {
  //           path: '/list/users/2',
  //           component: '/list/users',
  //           meta: {
  //             title: '女用户',
  //             icon: 'IconProLinkOutlined',
  //             hide: false,
  //             lang: {
  //               zh_TW: '女用戶',
  //               en: 'Female Users'
  //             }
  //           },
  //           children: [
  //             {
  //               path: '/list/users/details/2/:id',
  //               component: '/list/users/details',
  //               meta: {
  //                 title: '女用户详情',
  //                 icon: 'IconProLinkOutlined',
  //                 hide: true,
  //                 active: '/list/users/2',
  //                 lang: {
  //                   zh_TW: '女用戶詳情',
  //                   en: 'FemaleUserDetails'
  //                 }
  //               }
  //             }
  //           ]
  //         }
  //       ]
  //     }
  //   ]
  // },
  // 注释掉结果页面
  /*
  {
    path: '/result',
    component: null,
    meta: {
      title: '结果页面',
      icon: 'IconProCheckCircleOutlined',
      hide: false,
      lang: {
        zh_TW: '結果頁面',
        en: 'Result'
      }
    },
    redirect: '/result/success',
    children: [
      {
        path: '/result/success',
        component: '/result/success',
        meta: {
          title: '成功页',
          icon: 'IconProLinkOutlined',
          hide: false,
          lang: {
            zh_TW: '成功頁',
            en: 'Success'
          }
        }
      },
      {
        path: '/result/fail',
        component: '/result/fail',
        meta: {
          title: '失败页',
          icon: 'IconProLinkOutlined',
          hide: false,
          lang: {
            zh_TW: '失敗頁',
            en: 'Fail'
          }
        }
      }
    ]
  },
  */
  // 注释掉异常页面
  /*
  {
    path: '/exception',
    component: null,
    meta: {
      title: '异常页面',
      icon: 'IconProWarningOutlined',
      hide: false,
      lang: {
        zh_TW: '异常頁面',
        en: 'Exception'
      }
    },
    redirect: '/exception/403',
    children: [
      {
        path: '/exception/403',
        component: '/exception/403',
        meta: {
          title: '403',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/exception/404',
        component: '/exception/404',
        meta: {
          title: '404',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/exception/500',
        component: '/exception/500',
        meta: {
          title: '500',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      }
    ]
  },
  */
  // 注释掉个人中心
  
  {
    path: '/user',
    component: null,
    meta: {
      title: '个人中心',
      icon: 'IconProControlOutlined',
      hide: false,
      lang: {
        zh_TW: '個人中心',
        en: 'User'
      }
    },
    redirect: '/user/profile',
    children: [
      {
        path: '/user/profile',
        component: '/user/profile',
        meta: {
          title: '我的资料',
          icon: 'IconProUserOutlined',
          hide: false,
          lang: {
            zh_TW: '個人資料',
            en: 'Profile'
          }
        }
      },
      {
        path: '/user/message',
        component: '/user/message',
        meta: {
          title: '我的消息',
          icon: 'IconProMessageOutlined',
          hide: false,
          lang: {
            zh_TW: '我的消息',
            en: 'Message'
          }
        }
      }
    ]
  },
  // 注释掉扩展组件
  {
    path: '/extension',
    component: null,
    meta: {
      title: '扩展组件',
      icon: 'IconProAppstoreAddOutlined',
      hide: false,
      props: {
        badge: {
          isDot: true
        }
      }
    },
    redirect: '/extension/table',
    children: [
      {
        path: '/extension/table',
        component: '/extension/table',
        meta: {
          title: '高级表格',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/modal',
        component: '/extension/modal',
        meta: {
          title: '高级弹窗',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/message',
        component: '/extension/message',
        meta: {
          title: '消息提示',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/layout',
        component: '/extension/layout',
        meta: {
          title: '布局组件',
          icon: 'IconProLinkOutlined',
          hide: false,
          props: {
            badge: {
              isDot: true
            }
          }
        }
      },
      {
        path: '/extension/table-select',
        component: '/extension/table-select',
        meta: {
          title: '下拉表格',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/tree-select',
        component: '/extension/tree-select',
        meta: {
          title: '下拉树',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/upload',
        component: '/extension/upload',
        meta: {
          title: '文件上传',
          icon: 'IconProLinkOutlined',
          hide: false,
          props: {
            badge: {
              isDot: true
            }
          }
        }
      },
      {
        path: '/extension/icon',
        component: '/extension/icon',
        meta: {
          title: '图标选择',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/file',
        component: '/extension/file',
        meta: {
          title: '文件列表',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/split',
        component: '/extension/split',
        meta: {
          title: '分割面板',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/printer',
        component: '/extension/printer',
        meta: {
          title: '打印组件',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/text',
        component: '/extension/text',
        meta: {
          title: '文本组件',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/tag',
        component: '/extension/tag',
        meta: {
          title: '标签输入',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/avatar',
        component: '/extension/avatar',
        meta: {
          title: '头像组合',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/tour',
        component: '/extension/tour',
        meta: {
          title: '引导组件',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/menu',
        component: '/extension/menu',
        meta: {
          title: '导航菜单',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/check-card',
        component: '/extension/check-card',
        meta: {
          title: '可选卡片',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/watermark',
        component: '/extension/watermark',
        meta: {
          title: '水印组件',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/steps',
        component: '/extension/steps',
        meta: {
          title: '步骤条',
          icon: 'IconProLinkOutlined',
          hide: false,
          props: {
            badge: {
              value: 'New'
            }
          }
        }
      },
      {
        path: '/extension/viewer',
        component: '/extension/viewer',
        meta: {
          title: '查看器',
          icon: 'IconProLinkOutlined',
          hide: false,
          props: {
            badge: {
              value: 'New'
            }
          }
        }
      },
      {
        path: '/extension/segmented',
        component: '/extension/segmented',
        meta: {
          title: '分段器',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/tabs',
        component: '/extension/tabs',
        meta: {
          title: '标签页',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/qr-code',
        component: '/extension/qr-code',
        meta: {
          title: '二维码',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/bar-code',
        component: '/extension/bar-code',
        meta: {
          title: '条形码',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/regions',
        component: '/extension/regions',
        meta: {
          title: '城市选择',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/excel',
        component: '/extension/excel',
        meta: {
          title: '导入导出',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/dragsort',
        component: '/extension/dragsort',
        meta: {
          title: '拖拽排序',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/map',
        component: '/extension/map',
        meta: {
          title: '地图组件',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/player',
        component: '/extension/player',
        meta: {
          title: '视频播放',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/editor',
        component: '/extension/editor',
        meta: {
          title: '富文本框',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/markdown',
        component: '/extension/markdown',
        meta: {
          title: 'markdown',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/extension/monaco',
        component: '/extension/monaco',
        meta: {
          title: '代码编辑',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      }
    ]
  },
  // 注释掉内嵌页面
  {
    path: '/iframe',
    component: null,
    meta: {
      title: '内嵌页面',
      icon: 'IconProLinkOutlined',
      hide: false,
      lang: {
        zh_TW: '內嵌頁面',
        en: 'IFrame'
      }
    },
    redirect: '/iframe/eleadmin',
    children: [
      {
        path: '/iframe/eleadmin',
        component: 'https://www.eleadmin.com',
        meta: {
          title: '官网',
          icon: 'IconProLinkOutlined',
          hide: false,
          lang: {
            zh_TW: '官網',
            en: 'Website'
          }
        }
      },
      {
        path: '/iframe/eleadmin-doc',
        component: 'https://www.eleadmin.com/doc/eleadminplus/',
        meta: {
          title: '文档',
          icon: 'IconProLinkOutlined',
          hide: false,
          lang: {
            zh_TW: '檔案',
            en: 'Document'
          }
        }
      }
    ]
  },
  // 注释掉功能演示
  
  // {
  //   path: '/example',
  //   component: '/example',
  //   meta: {
  //     title: '功能演示',
  //     icon: 'IconProCompassOutlined',
  //     hide: false,
  //     lang: {
  //       zh_TW: '功能演示',
  //       en: 'Demo'
  //     }
  //   }
  // },
  // {
  //   path: '/course',
  //   component: null,
  //   meta: {
  //     title: '课程管理',
  //     icon: 'IconProBookOutlined',
  //     hide: false,
  //     lang: {
  //       zh_TW: '課程管理',
  //       en: 'Course'
  //     }
  //   },    
  //   children: [
  //     {
  //       path: '/course/list',
  //       component: '/course/course',
  //       meta: {
  //         title: '课程列表',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '課程列表',
  //           en: 'Course List'
  //         }
  //       }
  //     },
  //     {
  //       path: '/course/section',
  //       component: '/course/section',
  //       meta: {
  //         title: '课程章节',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         lang: {
  //           zh_TW: '課程章節列表',
  //           en: 'Course Section List'
  //         }
  //       }
  //     },
  //     {
  //       path: '/course/team',
  //       component: '/course/team',
  //       meta: {
  //         title: '团队列表',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         lang: {            
  //           en: 'Team List'
  //         }
  //       }
  //     },
  //     {
  //       path: '/course/team-member',
  //       component: '/course/team-member',
  //       meta: {
  //         title: '团队成员',
  //         icon: 'IconProLinkOutlined',
  //         hide: false,
  //         lang: {            
  //           en: 'Team Member List'
  //         }
  //       }
  //     }
  //   ]
  // }, 
  // {
  //   path: 'https://eleadmin.com/goods/17',
  //   component: null,
  //   meta: {
  //     title: '获取授权',
  //     icon: 'IconProProtectOutlined',
  //     hide: false,
  //     lang: {
  //       zh_TW: '獲取授權',
  //       en: 'Authorization'
  //     }
  //   }
  // }
];
