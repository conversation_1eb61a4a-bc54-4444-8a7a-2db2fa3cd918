import request from '@/utils/request';

/**
 * @async
 * @description 查询当前登录分销商获得的佣金分配明细列表 (分页)。
 * @param {Object} params - 查询参数
 * @param {String} [params.renewalTimeStart] - 佣金产生时间范围起始 (基于关联的续期时间) (可选)
 * @param {String} [params.renewalTimeEnd] - 佣金产生时间范围结束 (可选)
 * @param {String} [params.settlementTimeStart] - 结算到余额时间范围起始 (可选)
 * @param {String} [params.settlementTimeEnd] - 结算到余额时间范围结束 (可选)
 * @param {String} [params.status] - 按佣金状态筛选 ('PendingSettlement', 'Settled', 'IncludedInWithdrawal') (可选)
 * @param {String} [params.cardNumber] - 按产生佣金的年卡卡号查询 (可选)
 * @param {Number} [params.page] - 页码 (默认1) (可选)
 * @param {Number} [params.pageSize] - 每页数量 (默认10) (可选)
 */
export async function requestMyFeeDistroList(params) {
    const res = await request.get('/api/distro/fee-distro/my-list', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 查询佣金总账记录列表 (分页)，展示每次续期触发的佣金计算事件。 (管理员视角)
 * @param {Object} params - 查询参数
 * @param {String} [params.renewalId] - 按续期记录ID查询 (可选)
 * @param {String} [params.calculationTimeStart] - 计算时间范围起始 (可选)
 * @param {String} [params.calculationTimeEnd] - 计算时间范围结束 (可选)
 * @param {String} [params.triggeredByCardNumber] - 按触发的年卡卡号查询 (可选)
 * @param {String} [params.triggeredByTicketNumber] - 按触发的原始券号查询 (可选)
 * @param {Number} [params.page] - 页码 (默认1) (可选)
 * @param {Number} [params.pageSize] - 每页数量 (默认10) (可选)
 */
export async function requestFeeLedgerList(params) {
    const res = await request.get('/api/distro/fee-ledger/list', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 查询佣金总账记录列表 (分页)，展示每次续期触发的佣金计算事件。 (管理员视角)
 * @param {Object} params - 查询参数
 * @param {String} [params.renewalId] - 按续期记录ID查询 (可选)
 */
export async function requestFeeLedgerDetail(params) {
    const res = await request.get('/api/distro/fee-ledger/detail', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}