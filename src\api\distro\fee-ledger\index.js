import request from '@/utils/request';

/**
 * @async
 * @description 查询佣金总账记录列表 (分页)
 * @param {Object} params - 查询参数
 * @param {String} [params.renewalId] - 按续期记录ID查询 (可选)
 * @param {String} [params.calculationTimeStart] - 计算时间范围起始 (可选)
 * @param {String} [params.calculationTimeEnd] - 计算时间范围结束 (可选)
 * @param {String} [params.triggeredByCardNumber] - 按触发的年卡卡号查询 (可选)
 * @param {String} [params.triggeredByTicketNumber] - 按触发的原始券号查询 (可选)
 * @param {Number} [params.page] - 页码 (默认1) (可选)
 * @param {Number} [params.pageSize] - 每页数量 (默认10) (可选)
 */
export async function requestFeeLedgerList(params) {
  const res = await request.get('/api/distro/fee-ledger/list', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 获取佣金总账记录详情
 * @param {Object} params - 查询参数
 * @param {String} params.ledgerId - 总账记录ID (必填)
 */
export async function requestFeeLedgerDetail(params) {
  const res = await request.get('/api/distro/fee-ledger/detail', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
