<!-- 编辑弹窗 -->
<template>
    <ele-modal form :width="560" v-model="visible" :title="isTopLevel ? '创建分销商' : '添加分销商'" @open="handleOpen"
        destroy-on-close>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" @submit.prevent="">
            <el-form-item label="分销商名称" prop="name">
                <el-input clearable v-model="form.name" placeholder="请输入分销商名称" />
            </el-form-item>
            <el-form-item label="层级" prop="level">
                <el-input clearable v-model.number="form.level" disabled placeholder="请输入层级" />
            </el-form-item>
            <el-form-item v-if="form.parentVendorName" label="上级分销商" prop="parentVendorName">
                <el-input clearable v-model.number="form.parentVendorName" disabled />
            </el-form-item>
            <el-form-item label="联系人" prop="contactPerson">
                <el-input clearable v-model="form.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
            <el-form-item label="联系电话" prop="phoneNumber">
                <el-input clearable type="tel" v-model="form.phoneNumber" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="电子邮箱" prop="email">
                <el-input clearable type="email" v-model="form.email" placeholder="请输入邮箱地址" />
            </el-form-item>
            <el-form-item label="银行账户信息" prop="bankAccountInfo">
                <el-input clearable v-model="form.bankAccountInfo" placeholder="请输入银行账户信息" @input="handlebankAccount" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-radio-group v-model="form.status">
                    <el-radio value="Active" label="已激活" />
                    <el-radio value="Inactive" label="未激活" />
                    <el-radio value="Suspended" label="已停用" />
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" :loading="loading" @click="save">
                保存
            </el-button>
        </template>
    </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import { requestVendorAdd } from '@/api/distro/vendor/index';

/** 修改回显的数据 */
const props = defineProps({
    data: Object
});

/** 定义事件 */
const emit = defineEmits(['update:modelValue', 'done']);

/** 弹窗是否打开 */
const visible = defineModel('modelValue');

/** 是否是修改 */
const isTopLevel = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
    vendorId: null,
    userId: null,
    name: null,
    level: 1,
    parentVendorId: null,
    parentVendorName: null,
    roleId: null,
    contactPerson: null,
    phoneNumber: null,
    email: null,
    bankAccountInfo: null,
    status: 'Active',
});

/** 表单验证规则 */
const rules = reactive({
    userId: [
        {
            required: true,
            message: '关联用户不能为空',
            type: 'string',
            trigger: 'blur'
        }
    ],
    name: [
        {
            required: true,
            message: '分销商名称不能为空',
            type: 'string',
            trigger: 'blur'
        }
    ],
    level: [
        {
            required: true,
            message: '分销商层级不能为空',
            type: 'number',
            trigger: 'blur'
        }
    ]
});

/** 关闭弹窗 */
const handleCancel = () => {
    emit('update:modelValue', false);
};

// 卡号格式处理
const handlebankAccount = (value) => {
    // 卡号 每四位加一个空格
    const newValue = value.replace(/\D/g, "").replace(/(.{4})(?=\d)/g, "$1 ")
    form.bankAccountInfo = newValue;
}

/** 保存编辑 */
const save = () => {
    formRef.value?.validate?.((valid) => {
        if (!valid) {
            return;
        }
        loading.value = true;

        form.bankAccountInfo = form.bankAccountInfo?.replace(/\s/g, ''); // 去掉空格

        // 如果有密码字段且不为空，则加密
        const params = {
            ...form
        };

        console.log(params,'params');

        requestVendorAdd(params)
            .then((msg) => {
                loading.value = false;
                EleMessage.success(msg);
                handleCancel();
                emit('done');
            })
            .catch((e) => {
                loading.value = false;
                EleMessage.error(e.message);
            });
    });
};

/** 弹窗打开事件 */
const handleOpen = async () => {
    if (props.data) {
        const { vendorId, name, level } = Object.assign({}, props.data);
        assignFields({
            parentVendorName: name,
            level: level + 1,
            parentVendorId: vendorId,
            status: 'Active',
        });
        isTopLevel.value = true;
    } else {
        resetFields();
        isTopLevel.value = false;
    }
    nextTick(() => {
        nextTick(() => {
            formRef.value?.clearValidate?.();
        });
    });
};
</script>