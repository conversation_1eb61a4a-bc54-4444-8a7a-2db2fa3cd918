<!-- 编辑弹窗 -->
<template>
  <ele-modal form :width="460" v-model="visible" :title="isUpdate ? '修改文件预览' : '添加文件预览'" @open="handleOpen">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" @submit.prevent="">
      <el-form-item label="关联文件ID" prop="fileId">
        <el-input clearable v-model="form.fileId" placeholder="请输入关联文件ID" />
      </el-form-item>
      <el-form-item label="文件类型" prop="fileType">
        <el-input clearable v-model="form.fileType" placeholder="请输入文件类型" />
      </el-form-item>
      <el-form-item label="预览内容" prop="previewContent">
        <el-input type="textarea" :rows="4" clearable v-model="form.previewContent" placeholder="请输入预览内容" />
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-select clearable v-model="form.state" placeholder="请选择状态" class="ele-fluid">
          <el-option label="待处理" :value="0" />
          <el-option label="成功" :value="1" />
          <el-option label="处理中" :value="2" />
          <el-option label="失败" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="错误信息" prop="error">
        <el-input type="textarea" :rows="2" clearable v-model="form.error" placeholder="请输入错误信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import { updateFilePreview } from '@/api/system/filePreview';

/** 修改回显的数据 */
const props = defineProps({
  data: Object
});

/** 定义事件 */
const emit = defineEmits(['update:modelValue', 'done']);

/** 弹窗是否打开 */
const visible = defineModel('modelValue');

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: null,
  fileId: null,
  fileType: null,
  previewContent: null,
  state: null,
  error: null
});

/** 表单验证规则 */
const rules = reactive({
  fileId: [
    { required: true, message: '请输入关联文件ID', trigger: 'blur' }
  ],
  fileType: [
    { required: true, message: '请输入文件类型', trigger: 'blur' }
  ],
  state: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
});

/** 关闭弹窗 */
const handleCancel = () => {
  emit('update:modelValue', false);
};

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    const saveOrUpdate = updateFilePreview 
    
    saveOrUpdate(form)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

/** 弹窗打开事件 */
const handleOpen = () => {
  if (props.data) {
    assignFields(props.data);
    isUpdate.value = true;
  } else {
    resetFields();
    isUpdate.value = false;
  }
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  });
};
</script>