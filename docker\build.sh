#!/bin/bash
# 获取当期git分支的tag，如果有v前缀，则取v后面的数字作为版本号，没有符合的tag，结束脚本

# 获取最近一次提交关联的所有标签
tags=$(git tag --points-at HEAD)

# 标志变量，用于标记是否找到以 v 开头的标签
found=false

# 遍历所有标签
for tag in $tags; do
    if [[ $tag == v* ]]; then
        echo "版本号标签: $tag"
        found=true
        break
    fi
done

ver=${tag:1}

# 如果没有找到以 v 开头的标签
if [ "$found" = false ]; then
    echo "最近一次提交没有以 v 开头的标签"
    exit 1
fi

# 当脚本带有 -d 参数时忽略编译打包
if [ "$1" == "-d" ]; then
  echo "ignore build"
else
  npm run build
  # exit if yarn build failed
  if [ $? -ne 0 ]; then
    echo "npm build failed"
    exit 1
  fi
fi

# 获取当前脚本所在的绝对路径
current_dir=$(pwd)

# 源文件夹路径，这里假设dist就在当前目录下
source_dir="../dist"

# 目标文件夹路径
target_dir="$current_dir/html"

mkdir -p "$target_dir"

# 使用cp命令结合-r（递归）和-f（强制覆盖）选项来进行复制操作
cp -rf "$source_dir"/* "$target_dir"

echo "复制操作已完成。"

image=registry.cn-zhangjiakou.aliyuncs.com/cytong/vendor-distro-front
tag=v$ver

# build and push
docker build -t $image:latest -t $image:$tag .
docker push $image:latest
docker push $image:$tag

rm -rf $target_dir

echo 'pushed image to '$image':v'$ver
