import request from '@/utils/request';

/**
 * @async
 * @description 查询续期记录列表 (分页)。管理员可查所有，分销商根据权限查询与其分销链相关的卡的续期记录。
 * @param {Object} params - 查询参数
 * @param {String} [params.cardNumber] - 按年卡卡号查询 (可选)
 * @param {String} [params.openTicketNumber] - 按开卡券号查询 (可选)
 * @param {String} [params.openVendorId] - 按开卡分销商ID查询 (可选)
 * @param {Boolean} [params.feeProcessed] - 按结算状态查询 (可选)
 * @param {String} [params.renewalTimeStart] - 续期时间范围起始 (格式: yyyy-MM-dd HH:mm:ss) (可选)
 * @param {String} [params.renewalTimeEnd] - 续期时间范围结束 (格式: yyyy-MM-dd HH:mm:ss) (可选)
 * @param {String} [params.vendorId] - 查询由该分销商（及其下级）激活的卡的续期记录（基于卡关联的激活券的`original_vendor_chain`） (可选)
 * @param {Number} [params.page] - 页码 (默认1) (可选)
 * @param {Number} [params.pageSize] - 每页数量 (默认10) (可选)
 */
export async function requestRenewalList(params) {
    const res = await request.get('/api/distro/renewal/list', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description （内部/回调接口）接收第三方系统的年卡续期信息并同步到系统。
 * @param {Object} params - 查询参数
 * @param {String} [params.cardNumber] - 发生续期的年卡卡号 (系统需校验卡存在) (必填)
 * @param {String} [params.renewalTime] - 续期发生时间 (格式: yyyy-MM-dd HH:mm:ss) (必填)
 * @param {Number} [params.renewalAmount] -续期支付金额 (可选)
 * @param {String} [params.relatedProductId] - 本次续期购买的产品ID (如果可能变更) (可选)
 * @param {String} [params.newExpiryDate] - 续期后新的到期日期 (格式: yyyy-MM-dd, 用于更新Card表) (可选)
 */
export async function requestRenewalSync(params) {
    const res = await request.post('/api/distro/renewal/sync', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 结算续期记录
 * @param {Object} params - 请求参数
 * @param {String} params.renewalId - 续期记录ID
 */
export async function requestSettleRenewal(params) {
    const res = await request.post('/api/distro/renewal/settle', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}