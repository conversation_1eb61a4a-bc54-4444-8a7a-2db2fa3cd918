/**
 * axios实例
 */
import axios from "axios";
import { unref } from "vue";
import { ElMessageBox } from "element-plus/es";
import { API_BASE_URL, LAYOUT_PATH } from "@/config/setting";
import router from "@/router";
import { getToken, refreshToken } from "./token-util";
import { logout, toURLSearch } from "./common";

/** 创建axios实例 */
const service = axios.create({
  baseURL: API_BASE_URL
});

console.log(API_BASE_URL, '请求基本地址');

/**
 * 添加请求拦截器
 */
service.interceptors.request.use(
  (config) => {
    requestInterceptor(config);

    // 如果请求路径以api开头，将baseUrl换为/
    if (config.url?.startsWith('/api')) {
      config.baseURL = '/';
    }

    return config;
  },
  (error) => {
    console.error(error);
    return Promise.reject(new Error('网络错误'));
  }
);

/**
 * 添加响应拦截器
 */
service.interceptors.response.use(
  (res) => {
    // 确保响应数据存在
    if (!res || !res.data) {
      return Promise.reject(new Error('响应数据格式错误'));
    }
    const errorMessage = responseInterceptor(res);
    if (errorMessage) {
      return Promise.reject(new Error(errorMessage));
    }
    return res;
  },
  (error) => {
    console.error('请求错误:', error);
    return Promise.reject(new Error(error?.response?.data?.message || '网络错误'));
  }
);

/**
 * 请求拦截处理
 */
export function requestInterceptor(config) {
  // 添加token到header
  const token = getToken();
  if (token && config.headers) {
    config.headers['Authorization'] = token;
  }

  // get请求处理数组和对象类型参数
  if (config.method === 'get' && config.params) {
    config.url = toURLSearch(config.params, config.url);
    config.params = {};
  }
}

/**
 * 响应拦截处理
 */
export function responseInterceptor(res) {
  // 登录过期处理
  if (res.data?.code === 4001) {
    const { path, fullPath } = unref(router.currentRoute);
    if (path == LAYOUT_PATH) {
      logout(true, void 0, router.push);
    } else if (path !== '/login') {
      ElMessageBox.close();
      ElMessageBox.alert('登录状态已过期, 请退出重新登录!', '系统提示', {
        confirmButtonText: '重新登录',
        callback: (action) => {
          if (action === 'confirm') {
            logout(false, fullPath);
          }
        },
        type: 'warning',
        draggable: true
      });
    }
    return res.data.message;
  }
  // 续期token
  const newToken = res.headers['authorization'];
  if (newToken) {
    refreshToken(newToken);
  }
}

/**
 * 处理请求响应
 * @param {Promise} promise - 请求Promise
 * @param {boolean} needData - 是否需要返回data
 */
export const handleResponse = async (promise, needData = true) => {
  const res = await promise;
  if (res.data.code === 200) {
    // 如果需要data但返回的data为null，返回一个默认的空对象或空数组
    // 这样可以避免在调用方解构时出错
    if (needData && res.data.data === null) {
      console.warn('API返回的data为null，已转换为空对象/数组');

      // 检查URL是否是列表接口，如果是则返回默认的列表结构
      const url = res.config?.url || '';
      if (url.includes('/list')) {
        return { rows: [], total: 0 };
      }

      // 其他情况返回空对象
      return {};
    }

    return needData ? res.data.data : res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
};

export default service;
