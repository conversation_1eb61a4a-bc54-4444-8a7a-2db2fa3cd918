<template>
  <ele-card class="search-form">
    <el-form :model="form" :label-width="60" @submit.prevent="handleSearch">
      <el-row :gutter="8">
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="卡号">
            <el-input v-model="form.cardNumber" placeholder="请输入年卡卡号" clearable @keyup.enter="handleSearch" />
          </el-form-item>
        </el-col>
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="实体券">
            <el-input v-model="form.ticketNumber" placeholder="请输入实体券号" clearable @keyup.enter="handleSearch" />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="结算时间" :label-width="90">
            <date-range-picker v-model="form.settleTimeRange" start-placeholder="开始日期" end-placeholder="结束日期"
              value-format="YYYY-MM-DD" format="YYYY-MM-DD" style="width: 100%" />
          </el-form-item>
        </el-col>

        <el-col v-if="more" :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="续期时间" :label-width="90">
            <date-range-picker v-model="form.renewalTimeRange" start-placeholder="开始日期" end-placeholder="结束日期"
              style="width: 100%" />
          </el-form-item>
        </el-col>

        <el-col v-if="more" :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商">
            <vendor-select v-model="form.vendorId" placeholder="请选择分销商" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24" style="text-align: right">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-link type="primary" :underline="false" @click="toggleMore">
            <span class="mx-2">更多</span>
            <el-icon v-if="!more">
              <IconElArrowDown />
            </el-icon>
            <el-icon v-else>
              <IconElArrowUp />
            </el-icon>
          </el-link>
        </el-col>
      </el-row>

    </el-form>
  </ele-card>
</template>

<script setup>
import { ref } from 'vue';
import VendorSelect from '@/components/VendorSelect/index.vue';
import DateRangePicker from '@/components/DateRangePicker/index.vue';
import dayjs from 'dayjs';

const emit = defineEmits(['search']);

/** 表单数据 */
const form = ref({
  ledgerId: '',
  cardNumber: '',
  ticketNumber: '',
  vendorId: '',
  status: '',
  renewalTimeRange: [],
  settleTimeRange: [dayjs().subtract(2, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
});

/** 更多 */
const more = ref(false);
const toggleMore = () => {
  more.value = !more.value;
};

/** 搜索 */
const handleSearch = () => {
  const params = {
    ...form.value,
    renewalTimeStart: form.value.renewalTimeRange?.[0],
    renewalTimeEnd: form.value.renewalTimeRange?.[1],
    settleTimeStart: form.value.settleTimeRange?.[0],
    settleTimeEnd: form.value.settleTimeRange?.[1]
  };
  delete params.renewalTimeRange;
  delete params.settleTimeRange;
  emit('search', params);
};

/** 重置 */
const handleReset = () => {
  form.value = {
    ledgerId: '',
    cardNumber: '',
    ticketNumber: '',
    vendorId: '',
    status: '',
    renewalTimeRange: [],
    settleTimeRange: []
  };
  handleSearch();
};
</script>

<style scoped>
.search-form {
  margin-bottom: 16px;
}
</style>
