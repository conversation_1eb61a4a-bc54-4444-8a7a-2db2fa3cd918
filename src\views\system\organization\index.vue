<template>
  <ele-page>
    <organization-search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <ele-pro-table :lazy="true" ref="tableRef" row-key="id" :columns="columns" :datasource="datasource"
        :show-overflow-tooltip="true" :highlight-current-row="true" :export-config="{ fileName: '机构数据' }"
        :default-expand-all="false" :pagination="false" cache-key="systemOrganizationTable" 
        :load="tableLoad"
        :handle-done="handleDone">
        <template #toolbar>
          <el-button type="primary" class="ele-btn-icon" :icon="PlusOutlined" @click="openAdd()">
            新建
          </el-button>
          <el-button class="ele-btn-icon" :icon="ColumnHeightOutlined" @click="expandAll">
            展开全部
          </el-button>
          <el-button class="ele-btn-icon" :icon="VerticalAlignMiddleOutlined" @click="foldAll">
            折叠全部
          </el-button>
        </template>
        <template #orgType="{ row }">
          <dict-data code="orgType" :model-value="row.orgType" type="text" />
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" :disabled="row.orgType === 7" @click="openAdd(row)">
            添加
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="danger" :underline="false" @click="remove(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <organization-edit v-model="showEdit" :data="current" @done="handleSaveDone" />
  </ele-page>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import dayjs from 'dayjs';
import { ElMessageBox } from 'element-plus/es';
import { EleMessage, toTree, findTree } from 'ele-admin-plus/es';
import {
  PlusOutlined,
  ColumnHeightOutlined,
  VerticalAlignMiddleOutlined
} from '@/components/icons';
import OrganizationSearch from './components/organization-search.vue';
import OrganizationEdit from './components/organization-edit.vue';
import {
  listOrganizations,
  removeOrganization
} from '@/api/system/organization';

defineOptions({ name: 'SystemOrganization' });

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: 'index',
    columnKey: 'index',
    width: 50,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'orgName',
    label: '机构名称',
    sortable: 'custom',
    minWidth: 160
  },
  {
    prop: 'orgType',
    label: '机构类型',
    minWidth: 100,
    align: 'center',
    slot: 'orgType'
  },
  {
    prop: 'treeCode',
    label: '排序号',
    minWidth: 100,
    align: 'left'
  },
  {
    prop: 'tyOrgId',
    label: '教育云ID',
    minWidth: 100,
    align: 'center'
  },
  {
    prop: 'gmtCreate',
    label: '创建时间',
    sortable: 'custom',
    width: 180,
    align: 'center'
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 180,
    align: 'center',
    slot: 'action',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 当前编辑数据 */
const current = ref(null);

const refreshFunc = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = async ({ where, orders, parent }) => {
  const now = Date.now();
  const rsp = await listOrganizations({ ...where, ...orders, parentId: parent?.id || 0 });
  const data = rsp.rows;

  // 为子级数据记录父级的 ElTable 懒加载的 resolve 方法
  if (parent) {    
    data.forEach((d) => {
      if (parent && parent._tableResolve) {
        d._parentTableResolve = parent._tableResolve;
        d.parent = parent;
      }
    });
    parent._oldChildrenLength = parent.children?.length || 0;
  }

  // 重设 key 以解决数据可能无法更新的问题
  data.forEach((d) => {
    d.menuKey = d.menuId + '-' + now;
    d.updateTime = dayjs(now).format('YYYY-MM-DD HH:mm:ss.SSS');
  });
  return data;
};

/** 重写树表格懒加载方法 */
const tableLoad = (row, treeNode, resolve) => {
  // 记录 ElTable 懒加载的 resolve 方法, 刷新时需要这个方法来更新子级的数据  
  row._tableResolve = resolve;
  tableRef.value?.reload?.(void 0, row, resolve);
};

/** 刷新节点父级的子级数据 */
const reloadChildren = (row) => {
  if (row && row._tableResolve) {    
    tableRef.value?.reload?.(void 0, row, row._tableResolve);
  } else {
    reload();
  }
};

const handleSaveDone = () => {  
  refreshFunc.value && refreshFunc.value();
}

/** 刷新表格 */
const reload = (where) => {
  tableRef.value?.reload?.({ where });
};

const getChildOrgType = (orgType) => {
  if (!orgType) return '2';
  if (orgType === 7) return '7';

  let type = orgType + 1;
  if (type === 4) type = 5;
  return String(type);
}

/** 打开编辑弹窗 */
const openAdd = (row) => {
  current.value = {
    orgType: getChildOrgType(row?.orgType),
    parentId: row?.id || 0
  }
  refreshFunc.value = () => {
    reloadChildren(row);
  }
  showEdit.value = true;
}

const openEdit = (row) => {
  current.value = row;
  refreshFunc.value = () => {
    reloadChildren(row.parent);
  }
  showEdit.value = true;
};

/** 删除单个 */
const remove = (row) => {
  if (row.children?.length) {
    EleMessage.error('请先删除子节点');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除【' + row.orgName + '】吗?',
    '系统提示',
    { type: 'warning', draggable: true }
  )
    .then(() => {
      const loading = EleMessage.loading({
        message: '请求中..',
        plain: true
      });
      removeOrganization(row.id)
        .then((msg) => {
          loading.close();
          EleMessage.success(msg);
          reloadChildren(row.parent);
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    })
    .catch(() => { });
};

/** 展开全部 */
const expandAll = () => {
  tableRef.value?.toggleRowExpansionAll?.(true);
};

/** 折叠全部 */
const foldAll = () => {
  tableRef.value?.toggleRowExpansionAll?.(false);
};

/** 表格数据加载完成事件 */
const handleDone = ({ response }, parent) => {
  // 解决懒加载下子级无法清空
  if (parent && !response.length && parent._oldChildrenLength) {
    nextTick(() => {
      //console.log(parent);
      parent.hasChildren = false;
      parent.menuKey = parent.menuId + '-' + Date.now();
    });
  }
};
</script>
