---
description: 
globs: 
alwaysApply: false
---
# Git工作流规范

## 分支管理
1. 主分支
   - `main`: 生产环境分支
   - `develop`: 开发环境主分支

2. 功能分支
   - 格式：`feature/功能描述`
   - 从`develop`分支创建
   - 完成后合并回`develop`

3. 修复分支
   - 格式：`hotfix/问题描述`
   - 从`main`分支创建
   - 完成后同时合并到`main`和`develop`

## 提交规范

### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### Type类型
- `feat`: 新功能
- `fix`: 修复Bug
- `docs`: 文档更新
- `style`: 代码格式（不影响代码运行的变动）
- `refactor`: 重构
- `perf`: 性能优化
- `test`: 测试
- `chore`: 构建过程或辅助工具的变动

### 示例
```
feat(user): 添加用户登录功能

- 实现用户名密码登录
- 添加记住密码功能
- 添加登录状态保持

Closes #123
```

## 代码审查
1. 所有代码变更必须通过Pull Request提交
2. 至少需要一个审查者批准
3. CI检查必须通过
4. 解决所有代码评审意见后才能合并

## 发布流程
1. 在`develop`分支完成功能开发和测试
2. 创建`release/x.x.x`分支
3. 在release分支进行最终测试和bug修复
4. 测试通过后合并到`main`分支
5. 在`main`分支打标签发布

## 版本号规范
采用语义化版本号 (Semantic Versioning)：
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

