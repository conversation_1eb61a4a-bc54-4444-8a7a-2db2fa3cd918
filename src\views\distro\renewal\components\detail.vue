<template>
    <ele-drawer v-model="visible" title="续期记录详情" :size="800" style="max-width: 100%;" @open="open" @close="close">        
        <div class="subtitle mt--4"><span>基本信息</span></div>
        <el-descriptions :column="2" border>
            <el-descriptions-item label="续期ID">
                <ele-copyable>{{ detail.renewalId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="年卡ID">
                <ele-copyable>{{ detail.cardId }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="年卡卡号">
                <ele-copyable>{{ detail.cardNumber }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="产品名称">{{ detail.productName }}</el-descriptions-item>
            <el-descriptions-item label="开卡券号">
                <ele-copyable>{{ detail.openTicketNumber }}</ele-copyable>
            </el-descriptions-item>
            <el-descriptions-item label="开卡分销商">{{ detail.openVendorName }}</el-descriptions-item>
            <el-descriptions-item label="续期金额">{{ detail.renewalAmount }}</el-descriptions-item>
            <el-descriptions-item label="结算状态">
                <ele-dot v-if="detail.feeProcessed" type="success" text="已结算" :ripple="false" />
                <ele-dot v-else type="danger" text="未结算" />
            </el-descriptions-item>
            <el-descriptions-item label="续期时间">{{ detail.renewalTime }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ detail.createTime }}</el-descriptions-item>
        </el-descriptions>

        <!-- 佣金分配明细 -->
        <div class="subtitle mt-2"><span>佣金分配明细</span></div>
        <ele-pro-table 
            ref="tableRef" 
            row-key="feeDistroId" 
            :toolbar="false"
            :columns="columns" 
            :datasource="feeDistroList" 
            :pagination="false" 
            :show-overflow-tooltip="false"
        >
            <template #status="{ row }">
                <el-tag :type="getStatusType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
            </template>
        </ele-pro-table>
    </ele-drawer>
</template>

<script setup>
import { computed, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { requestRenewalDetail } from '@/api/distro/renewal/index';

const props = defineProps({
    visible: { 
        type: Boolean, 
        default: false 
    },
    data: { 
        type: Object, 
        default: () => ({}) 
    }
});

const emit = defineEmits(['close', 'update:visible']);

const visible = computed({
    get() { 
        return props.visible 
    },
    set(value) { 
        emit('update:visible', value);
    }
});

let detail = reactive({
    renewalId: '',
    cardId: '',
    cardNumber: '',
    productName: '',
    openTicketNumber: '',
    openVendorName: '',
    renewalAmount: '',
    feeProcessed: false,
    renewalTime: '',
    createTime: '',
});

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
    {
        prop: 'vendorId',
        label: '分销商ID',
        align: 'center',
        width: 120
    },
    {
        prop: 'vendorName',
        label: '分销商名称',
        align: 'left',
        minWidth: 120
    },
    {
        prop: 'feeAmount',
        label: '分配金额',
        align: 'right',
        width: 100
    },
    {
        prop: 'status',
        label: '状态',
        align: 'center',
        width: 100,
        slot: 'status'
    },
    {
        prop: 'settlementTime',
        label: '结算时间',
        align: 'center',
        width: 160
    }
]);

/** 获取状态类型 */
const getStatusType = (status) => {
    const types = {
        'PendingSettlement': 'warning',
        'Settled': 'success',
        'IncludedInWithdrawal': 'info'
    };
    return types[status] || 'info';
};

/** 获取状态标签 */
const getStatusLabel = (status) => {
    const labels = {
        'PendingSettlement': '待结算',
        'Settled': '已结算',
        'IncludedInWithdrawal': '已提现'
    };
    return labels[status] || status;
};

/** 表格数据源 */
const feeDistroList = () => {
    // 这里应该调用实际的API获取佣金分配明细列表
    // 为了演示，这里返回模拟数据
    return Promise.resolve({
        list: [],
        count: 0
    });
};

async function open() {
    try {
        // 重置 detail 数据
        Object.assign(detail, {
            renewalId: '',
            cardId: '',
            cardNumber: '',
            productName: '',
            openTicketNumber: '',
            openVendorName: '',
            renewalAmount: '',
            feeProcessed: false,
            renewalTime: '',
            createTime: '',
        });

        if (!props.data?.renewalId) {
            throw new Error('获取续期记录ID失败');
        }

        // 获取续期记录详情
        const result = await requestRenewalDetail({
            renewalId: props.data.renewalId
        });
        Object.assign(detail, result);

        // 刷新佣金分配明细表格
        tableRef.value?.reload?.();
    } catch (error) {
        ElMessage.error(error.message || '获取数据失败');
    }
}

function close() {
    emit('close');
}
</script>

<style scoped lang="scss">
:deep(.el-descriptions__label) {
    font-weight: bold;
}

.subtitle {
    position: relative;
    margin: 16px 0;
    padding-left: 10px;
    color: #1f2f3d;
    font-weight: bold;
    font-size: 16px;
    
    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 14px;
        background: var(--el-color-primary);
        border-radius: 2px;
    }
}

.mt--4 {
    margin-top: -16px;
}

.mt-2 {
    margin-top: 8px;
}
</style> 