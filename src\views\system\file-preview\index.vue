<template>
  <ele-page>
    <!-- 搜索表单 -->
    <file-preview-search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table sticky ref="tableRef" row-key="id" :columns="columns" :datasource="datasource"
        :show-overflow-tooltip="true" :highlight-current-row="true" :export-config="{ fileName: '文件预览' }"
        cache-key="systemFilePreviewTable">
        <template #toolbar>
          <el-button v-permission="'system:file-preview:add'" type="primary" class="ele-btn-icon" :icon="PlusOutlined"
            @click="openEdit()">
            新建
          </el-button>          
        </template>

        <!-- action 操作列 -->
        <template #action="{ row }">
          <el-link v-permission="'system:file-preview:edit'" type="primary" :underline="false" @click="openEdit(row)">
            编辑
          </el-link>
          <el-divider v-permission="'system:file-preview:remove'" direction="vertical" style="margin: 0 8px" />
          <el-link v-permission="'system:file-preview:remove'" type="danger" :underline="false" @click="rebuild(row)">
            生成
          </el-link>
        </template>

        <!-- fileName 文件名 -->
        <template #fileName="{ row }">
          {{ row.file.fileName }}
        </template>

        <!-- state 状态 -->
        <template #state="{ row }">
          <el-tag :type="getStateType(row.state)">
            {{ getStateText(row.state) }}
          </el-tag>
        </template>

        <!-- previewContent 预览内容 -->
        <template #previewContent="{ row }">
          <div v-if="row.fileType === 'PPT'" class="flex flex-wrap justify-center gap-2">
            <span>{{ getPreviewContentDescr(row) }}</span>
            <el-link type="primary" size="small" @click="openImageViewer(row)">
              预览
            </el-link>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>

    <!-- 编辑弹窗 -->
    <file-preview-edit v-model="showEdit" :data="current" @done="reload" />

    <!-- 图片预览 -->
    <ele-image-viewer
            v-model="showImageViewer"
            :url-list="imageUrlList"
            :initial-index="0"
        />
  </ele-page>
</template>

<script setup>
import { getFilePreviewList, removeFilePreview } from '@/api/system/filePreview';
import {
  PlusOutlined
} from '@/components/icons';
import { EleMessage } from 'ele-admin-plus/es';
import { ElMessageBox } from 'element-plus/es';
import { ref } from 'vue';
import FilePreviewEdit from './components/file-preview-edit.vue';
import FilePreviewSearch from './components/file-preview-search.vue';

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'fileName',
    label: '文件名',
    align: 'left',
    minWidth: 200,
    slot: 'fileName'
  },
  {
    prop: 'fileSize',
    label: '文件大小',
    align: 'center',
    minWidth: 60,
    formatter: (row) => {
      if (row.file && row.file.fileSize) {
        return (row.file.fileSize / 1024 / 1024).toFixed(2) + ' MB';
      }
      return '';
    }
  },
  {
    prop: 'fileType',
    label: '文件类型',
    align: 'center',
    minWidth: 60
  },
  {
    prop: 'state',
    label: '状态',
    align: 'center',
    minWidth: 60,
    slot: 'state'
  },
  {
    prop: 'previewContent',
    label: '预览内容',
    align: 'center',
    minWidth: 100,
    slot: 'previewContent'
  },
  {
    prop: 'error',
    label: '错误信息',
    align: 'center',    
    minWidth: 200
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',    
    hideInTable: true,
    minWidth: 110
  },
  {
    prop: 'updateTime',
    label: '更新时间',
    align: 'center',
    minWidth: 110
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 120,
    align: 'center',
    slot: 'action',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 图片预览 */
const showImageViewer = ref(false);
const imageUrlList = ref([]);

/** 打开图片预览 */
const openImageViewer = (row) => {
  imageUrlList.value = row.imageList;
  showImageViewer.value = true;
};

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = async ({ pages, where }) => {
  if (!where.sort) {
    where.sort = 'createTime';
    where.order = 'desc';
  }
  const result = await getFilePreviewList({ ...where, ...pages });  
  result.rows.forEach(row => {
    if (row.fileType === 'PPT') {
      row.imageList = row.previewContent ? row.previewContent.split(',') : [];
    }
  });
  return result;
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ where });
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 删除单个 */
const rebuild = (row) => {
  ElMessageBox.confirm(
    '是否确认重新生成该文件预览记录?',
    '系统提示',
    { type: 'warning', draggable: true }
  )
    .then(() => {
      // const loading = EleMessage.loading({
      //   message: '请求中..',
      //   plain: true
      // });
      // removeFilePreview(row.id)
      //   .then(() => {
      //     loading.close();
      //     EleMessage.success('重新生成成功');
      //     reload();
      //   })
      //   .catch((e) => {
      //     loading.close();
      //     EleMessage.error(e.message);
      //   });
    })
    .catch(() => { });
};

/** 状态配置 */
const stateConfig = {
  0: { text: '待处理', type: 'info' },
  1: { text: '成功', type: 'success' },
  2: { text: '处理中', type: 'warning' },
  3: { text: '失败', type: 'danger' }
};

/** 获取状态文本 */
const getStateText = (state) => {
  return stateConfig[state]?.text || '未知';
};

/** 获取状态类型 */
const getStateType = (state) => {
  return stateConfig[state]?.type || 'info';
};

/** 获取预览内容描述 */
const getPreviewContentDescr = (row) => {
  if (row.fileType === 'PPT') {     
    return `${row.imageList.length} 张图片`;
  }
  return row.previewContent;
};

</script>