<template>
  <ele-card header="可搜索">
    <option-item label="单选">
      <div style="max-width: 240px">
        <ele-tree-select
          clearable
          filterable
          placeholder="请选择"
          v-model="selectedValue"
          :popper-width="240"
          :tree-props="treeProps"
          :popper-options="{ strategy: 'fixed' }"
        />
      </div>
    </option-item>
    <option-item label="多选" style="margin-top: 18px">
      <div style="max-width: 240px">
        <ele-tree-select
          clearable
          multiple
          filterable
          placeholder="请选择"
          v-model="selectedValue2"
          :popper-width="240"
          :tree-props="treeProps2"
          :popper-options="{ strategy: 'fixed' }"
        />
      </div>
    </option-item>
  </ele-card>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { EleMessage, toTree } from 'ele-admin-plus/es';
  import OptionItem from '@/views/extension/avatar/components/option-item.vue';
  import { listMenus } from '@/api/system/menu';

  /** 选中值 */
  const selectedValue = ref();

  /** 树属性 */
  const treeProps = reactive({
    height: 266,
    data: [],
    props: { value: 'menuId', label: 'title' },
    expandOnClickNode: false
  });

  /** 选中值 */
  const selectedValue2 = ref([]);

  /** 树属性 */
  const treeProps2 = reactive({
    height: 266,
    data: [],
    props: { value: 'menuId', label: 'title' },
    expandOnClickNode: true
  });

  listMenus()
    .then((list) => {
      treeProps.data = toTree({
        data: list,
        idField: 'menuId',
        parentIdField: 'parentId'
      });
      treeProps2.data = toTree({
        data: list,
        idField: 'menuId',
        parentIdField: 'parentId'
      });
    })
    .catch((e) => {
      EleMessage.error(e.message);
    });
</script>
