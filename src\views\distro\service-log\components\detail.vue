<template>
  <ele-drawer v-model="visible" title="业务日志详情" :size="800" style="max-width: 100%;" @open="onOpen" @close="onClose">
    <div v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="日志ID">{{ detail.logId }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">
          <dict-data type="text" code="serviceType" v-model="detail.serviceType" />
        </el-descriptions-item>
        <el-descriptions-item label="操作描述" :span="2">{{ detail.description }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detail.remark }}</el-descriptions-item>

        <el-descriptions-item label="实体券">
          <ele-copyable>{{ detail.ticket?.ticketNumber }}</ele-copyable>
        </el-descriptions-item>
        <el-descriptions-item label="券ID">
          <ele-copyable>{{ detail.ticketId }}</ele-copyable>
        </el-descriptions-item>

        <el-descriptions-item v-if="detail.startNum || detail.endNum" label="券号段" :span="2">
          {{ detail.startNum }} ~ {{ detail.endNum }}
        </el-descriptions-item>

        <el-descriptions-item label="年卡">
          <ele-copyable v-if="detail.card">{{ detail.card?.cardNumber }}</ele-copyable>
        </el-descriptions-item>
        <el-descriptions-item label="年卡ID">
          <ele-copyable v-if="detail.cardId">{{ detail.cardId }}</ele-copyable> 
        </el-descriptions-item>

        <el-descriptions-item label="分销商">          
          <ele-copyable v-if="detail.vendor">{{ detail.vendor?.name }}</ele-copyable>
        </el-descriptions-item>
        <el-descriptions-item label="分销商ID">
          <ele-copyable v-if="detail.vendorId">{{ detail.vendorId }}</ele-copyable>
        </el-descriptions-item>

        <el-descriptions-item label="操作人">{{ detail.createByUser?.username }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ detail.createTime }}</el-descriptions-item>

        <el-descriptions-item v-if="detail.errorMsg" label="错误信息">
          <pre style="color: #F56C6C;">{{ detail.errorMsg }}</pre>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </ele-drawer>
</template>

<script setup>
import { ref, computed } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { requestServiceLogDetail } from '@/api/distro/service-log/index';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: null
  }
});

// 定义事件
const emit = defineEmits(['close']);

// 可见性控制
const visible = computed({
  get: () => props.visible,
  set: () => emit('close')
});

// 加载状态
const loading = ref(false);

// 详情数据
const detail = ref({});

// 打开抽屉时加载详情
const onOpen = async () => {
  if (props.data?.logId) {
    await loadDetail(props.data.logId);
  }
};

// 关闭时清空数据
const onClose = () => {
  detail.value = {};
};

// 加载详情数据
const loadDetail = async (logId) => {
  if (!logId) return;

  loading.value = true;
  try {
    const res = await requestServiceLogDetail({ logId });
    detail.value = res || {};
  } catch (e) {
    EleMessage.error(e.message || '获取详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '0': '正常',
    '1': '异常'
  };
  return statusMap[status] || status;
};

// 获取状态标签类型
const getStatusType = (status) => {
  const typeMap = {
    '0': 'success',
    '1': 'danger'
  };
  return typeMap[status] || 'info';
};

// 格式化JSON显示
const formatJson = (jsonStr) => {
  if (!jsonStr) return '';

  try {
    // 如果是字符串，尝试解析为JSON对象
    const jsonObj = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;
    return JSON.stringify(jsonObj, null, 2);
  } catch (e) {
    // 如果解析失败，直接返回原字符串
    return jsonStr;
  }
};
</script>

<style scoped>
pre {
  margin: 0;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  max-height: 300px;
  overflow: auto;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>