import request from '@/utils/request'

/**
 * @async
 * @description 查询佣金规则集列表 (分页)
 * @param { Object } params
 * @param { String } params.ruleSetName 规则集名称 (可选, 模糊查询)
 * @param { Boolean } params.isDefault  是否为默认规则 (可选)
 * @param { Number } params.page 页码 (默认1)
 * @param { Number } params.pageSize 每页数量 (默认10)
 */
export async function requestFeeRuleList(params) {
    const res = await request.get('/api/distro/fee-rule-set/list', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 获取佣金规则集详细信息及其层级明细
 * @param { Object } params
 * @param { String } params.ruleSetId 规则集ID (必填)
 */
export async function requestFeeRuleDetail(params) {
    const res = await request.get('/api/distro/fee-rule-set/detail', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 新增佣金规则集（包含层级明细）
 * @param { Object } params
 * @param { String } params.ruleSetName 规则集名称 (必填)
 * @param { String } params.description 描述 (可选)
 * @param { Array } params.levels 层级规则明细列表 (必填)
 * @param { Number } params.levels[].level 层级 (必填)
 * @param { String } params.levels[].feeType 佣金类型 (必填,'Percentage', 'FixedAmount')
 * @param { Number } params.levels[].feeValue 佣金值 (必填)
 */
export async function requestFeeRuleAdd(params) {
    const res = await request.post('/api/distro/fee-rule-set/add', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 更新佣金规则集信息（包含层级明细）
 * @param { Object } params
 * @param { String } params.ruleSetId 规则集ID (必填)
 * @param { String } params.ruleSetName 规则集名称 (可选)
 * @param { String } params.description 描述 (可选)
 * @param { Array } params.levels 全量更新 层级规则明细列表。 (可选)
 * @param { Number } params.levels[].level 对应的分销商层级 (必填)
 * @param { String } params.levels[].feeType 佣金类型 (必填,'Percentage', 'FixedAmount')
 * @param { Number } params.levels[].feeValue 佣金值 (必填)
 * @param { String } params.levels[].ruleLevelId **更新或删除时需要**。用于标识已存在的层级规则。如果是新增的层级规则，此字段为空。 (可选)
 * @param { Boolean } params.levels[]._delete 用于标记删除。如果 `ruleLevelId` 存在且 `_delete` 为 true，则删除该层级规则。 (可选, 默认false)
 */
export async function requestFeeRuleUpdate(params) {
    const res = await request.post('/api/distro/fee-rule-set/update', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 删除佣金规则集 (逻辑删除)。需要检查是否被产品关联，若有关联，建议不允许删除或给出提示。不能删除默认规则集。
 * @param { Object } params
 * @param { String } params.ruleSetId 要删除的规则集ID (必填)
 */
export async function requestFeeRuleDelete(params) {
    const res = await request.post('/api/distro/fee-rule-set/delete', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 设置佣金规则集为默认
 * @param { Object } params
 * @param { String } params.ruleSetId 要设置为默认的规则集ID (必填)
 */
export async function requestFeeRuleSetDefault(params) {
    const res = await request.post('/api/distro/fee-rule-set/set-default', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}