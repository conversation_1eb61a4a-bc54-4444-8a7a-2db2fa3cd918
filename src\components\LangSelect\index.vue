<template>
  <el-select
    v-model="selectedValue"
    @change="handleChange"
    :placeholder="placeholder"
    clearable
    :multiple="multiple"
    :disabled="disabled"
    :collapse-tags="multiple"
    :collapse-tags-tooltip="multiple"
    :max-collapse-tags="5"
    :popper-options="{ strategy: 'fixed' }"
    style="width: 100%"
    :loading="loading"
  >
    <el-option
      v-for="item in langOptions"
      :key="item.code"
      :label="item.description"
      :value="item.code"
    />
  </el-select>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { getLangList } from '@/api/oj/langs';
import { EleMessage } from 'ele-admin-plus/es';
import { useUserStore } from '@/store/modules/user';

defineOptions({ name: 'LangSelect' });

const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: undefined
  },
  placeholder: {
    type: String,
    default: '请选择编程语言'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否使用静态字典数据
  useStatic: {
    type: Boolean,
    default: false
  },
  // 是否加载当前域的语言列表
  loadDomainLangs: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 用户存储，用于获取当前域ID
const userStore = useUserStore();

const selectedValue = computed({
  get() {
    if (props.multiple) {
      // 如果是多选模式且值为字符串（如逗号分隔的格式），则转为数组
      if (props.modelValue && typeof props.modelValue === 'string') {
        return props.modelValue.split(',').filter(item => item.trim() !== '');
      }
      return props.modelValue || [];
    }
    return props.modelValue;
  },
  set(value) {
    if (props.multiple && Array.isArray(value)) {
      // 多选模式，将数组转为逗号分隔的字符串再emit
      const strValue = value.join(',');
      emit('update:modelValue', strValue);
    } else {
      // 单选模式
      emit('update:modelValue', value);
    }
  }
});

// 处理选择事件
const handleChange = (value) => {
  if (props.multiple && Array.isArray(value)) {
    // 如果是多选且为数组，转换为逗号分隔的字符串
    const strValue = value.join(',');
    emit('change', strValue);
    // 同时更新modelValue，确保一致性
    emit('update:modelValue', strValue);
  } else {
    emit('change', value);
  }
};

// 监听modelValue变化，确保当它变化时重新加载选项
watch(
  () => props.modelValue,
  (newVal) => {
    // 如果modelValue发生变化且不是由内部selectedValue变化引起的
    // 这里不需要做特别处理，因为selectedValue是一个computed，会自动响应modelValue的变化
    console.log('modelValue变化:', newVal);
  }
);

// 监听useStatic属性变化
watch(
  () => props.useStatic,
  () => {
    // 当useStatic属性变化时，重新加载语言选项
    loadLangOptions();
  }
);

// 编程语言选项
const langOptions = ref([]);

// 加载状态
const loading = ref(false);

// 静态语言选项
const staticLangOptions = [
  { code: 'bash', description: 'Bash', type: 'string' },
  { code: 'c', description: 'C', type: 'string' },
  { code: 'cc', description: 'C++', type: 'string' },
  { code: 'cc.cc98', description: 'C++98', type: 'string' },
  { code: 'cc.cc98o2', description: 'C++98(O2)', type: 'string' },
  { code: 'cc.cc11', description: 'C++11', type: 'string' },
  { code: 'cc.cc11o2', description: 'C++11(O2)', type: 'string' },
  { code: 'cc.cc14', description: 'C++14', type: 'string' },
  { code: 'cc.cc14o2', description: 'C++14(O2)', type: 'string' },
  { code: 'cc.cc17', description: 'C++17', type: 'string' },
  { code: 'cc.cc17o2', description: 'C++17(O2)', type: 'string' },
  { code: 'cc.cc20', description: 'C++20', type: 'string' },
  { code: 'cc.cc20o2', description: 'C++20(O2)', type: 'string' },
  { code: 'pas', description: 'Pascal', type: 'string' },
  { code: 'java', description: 'Java', type: 'string' },
  { code: 'kt', description: 'Kotlin', type: 'string' },
  { code: 'kt.jvm', description: 'Kotlin/JVM', type: 'string' },
  { code: 'py', description: 'Python', type: 'string' },
  { code: 'py.py2', description: 'Python 2', type: 'string' },
  { code: 'py.py3', description: 'Python 3', type: 'string' },
  { code: 'py.pypy3', description: 'PyPy3', type: 'string' },
  { code: 'php', description: 'PHP', type: 'string' },
  { code: 'rs', description: 'Rust', type: 'string' },
  { code: 'hs', description: 'Haskell', type: 'string' },
  { code: 'js', description: 'NodeJS', type: 'string' },
  { code: 'go', description: 'Golang', type: 'string' },
  { code: 'rb', description: 'Ruby', type: 'string' },
  { code: 'cs', description: 'C#', type: 'string' },
  { code: 'r', description: 'R', type: 'string' }
];

// 加载语言选项
const loadLangOptions = async () => {
  console.log("LangSelect组件开始加载语言选项");
  console.log("props设置:", {
    useStatic: props.useStatic,
    loadDomainLangs: props.loadDomainLangs,
    multiple: props.multiple
  });
  
  // 如果使用静态数据，则直接使用静态选项
  if (props.useStatic) {
    console.log("使用静态语言数据");
    langOptions.value = [...staticLangOptions];
    console.log("静态语言选项:", langOptions.value);
    return;
  }

  try {
    loading.value = true;
    const params = {};
    
    // 不再根据loadDomainLangs属性传递domainId参数
    // 始终获取所有语言列表
    console.log("请求获取所有语言列表");

    // 打印请求参数
    console.log("请求语言列表参数:", params);

    try {
      const result = await getLangList(params);
      console.log("API返回语言列表:", result);
      
      if (result) {
        // 检查返回数据格式并处理
        if (Array.isArray(result)) {
          console.log("API直接返回数组格式");
          langOptions.value = [...result];
        } else if (result.data && Array.isArray(result.data)) {
          console.log("API返回data字段包含数组");
          // 如果返回的是 {data: [...]} 格式，转换数据结构
          const convertedOptions = result.data.map(item => ({
            code: item.dataCode,
            description: item.dataName,
            type: 'string'
          }));
          console.log("转换后的语言选项:", convertedOptions);
          langOptions.value = convertedOptions;
        } else {
          console.warn('获取语言列表返回格式不支持:', result);
          console.log("回退到静态语言选项");
          langOptions.value = [...staticLangOptions];
        }
        
        console.log("最终语言选项数量:", langOptions.value.length);
        console.log("语言选项内容:", langOptions.value);
        
        // 检查当前选中的值是否在结果中
        if (props.multiple && props.modelValue) {
          const selectedCodes = typeof props.modelValue === 'string' 
            ? props.modelValue.split(',').filter(item => item.trim() !== '')
            : Array.isArray(props.modelValue) ? props.modelValue : [];
          
          console.log('当前选中的语言代码:', selectedCodes);
          
          // 检查是否所有选中的代码都在加载的选项中
          const validCodes = selectedCodes.filter(code => 
            langOptions.value.some(item => item.code === code)
          );
          
          if (validCodes.length !== selectedCodes.length) {
            console.warn('有选中的语言代码不在加载的选项中:', 
              selectedCodes.filter(code => !validCodes.includes(code))
            );
          }
        }
      } else {
        console.warn('获取语言列表返回为空');
        console.log("回退到静态语言选项");
        langOptions.value = [...staticLangOptions];
      }
    } catch (e) {
      console.error('请求语言API错误:', e);
      console.log("API错误后回退到静态语言选项");
      langOptions.value = [...staticLangOptions];
    }
  } catch (e) {
    console.error('获取语言列表失败:', e);
    // 如果API请求失败，fallback到静态选项
    langOptions.value = [...staticLangOptions];
    EleMessage.error('获取语言列表失败: ' + e.message);
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载语言选项
onMounted(() => {
  loadLangOptions();
});

// 对外公开的API
defineExpose({
  // 暴露编程语言选项，可能对其他组件有用
  getLangOptions: () => langOptions.value,
  // 重新加载语言选项
  reload: loadLangOptions,
  // 调试方法 - 获取当前下拉框状态
  debug: () => {
    return {
      langOptions: langOptions.value,
      selectedValue: selectedValue.value,
      modelValue: props.modelValue,
      userStoreId: userStore.currentDomainId
    };
  },
  // 强制使用静态数据
  useStaticData: () => {
    langOptions.value = [...staticLangOptions];
    console.log("强制使用静态数据:", langOptions.value.length);
    return langOptions.value.length;
  }
});
</script>
