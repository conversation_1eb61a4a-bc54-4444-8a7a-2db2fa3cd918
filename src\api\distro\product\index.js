import request from '@/utils/request';

/**
 * @async
 * @description 查询年卡产品列表 (分页)
 * @param { Object } params
 * @param { String } params.productName 产品名称 (可选, 模糊查询)
 * @param { Boolean } params.isActive  是否启用 (可选)
 * @param { Number } params.page 页码 (默认1)
 * @param { Number } params.pageSize 每页数量 (默认10)
 */
export async function requestProductList(params) {
  const res = await request.get('/api/distro/product/list', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 获取年卡产品详细信息dxxxxxxxxxxxxxxx
 * @param { Object } params
 * @param { String } params.productId 产品ID (必填)
 */
export async function requestProductDetail(params) {
  const res = await request.get('/api/distro/product/detail', {params});
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 新增年卡产品
 * @param { Object } params
 * @param { String } params.productName 产品名称 (必填)
 * @param { String } params.description 产品描述 (可选)
 * @param { String } params.feeRuleSetId 关联的特定佣金规则集ID (不填或传空则使用系统默认规则，可选)
 * @param { Boolean } params.isActive 是否启用 (可选，默认true)
 */
export async function requestProductAdd(params) {
  const res = await request.post('/api/distro/product/add', params);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 更新年卡产品信息
 * @param { Object } params
 * @param { String } params.productId 要更新的产品ID (必填)
 * @param { String } params.productName 产品名称 (可选)
 * @param { String } params.description 产品描述 (可选)
 * @param { String } params.feeRuleSetId 关联的特定佣金规则集ID (可选，传 null 或空字符串表示清除特定规则，使用默认规则)
 * @param { Boolean } params.isActive 是否启用 (可选)
 */
export async function requestProductUpdate(params) {
  const res = await request.post('/api/distro/product/update', params);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/** 
 * @async
 * @description: 删除年卡产品 (通常是逻辑删除或标记为禁用，需要检查是否有未处理的券或卡依赖)
 * @param { Object } params
 * @param { String } params.productId 要删除的产品ID (必填)
 */
export async function requestProductDelete(params) {
  const res = await request.post('/api/distro/product/delete', params);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}