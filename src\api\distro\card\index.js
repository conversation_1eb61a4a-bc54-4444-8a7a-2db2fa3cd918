import request from '@/utils/request';

/**
 * @async
 * @description 查询年卡信息列表 (分页)
 * @param {Object} params - 查询参数
 * @param {String} [params.cardNumber] - 按年卡卡号精确查询 (可选)
 * @param {String} [params.thirdPartyUserId] - 按第三方用户ID查询 (可选)
 * @param {String} [params.activationTicketNumber] - 按激活时使用的实体券号查询 (可选)
 * @param {String} [params.status] - 按年卡状态筛选 ('Active', 'Expired', 'Cancelled') (可选)
 * @param {String} [params.expiryDateStart] - 到期日期范围起始 (格式: yyyy-MM-dd) (可选)
 * @param {String} [params.expiryDateEnd] - 到期日期范围结束 (格式: yyyy-MM-dd) (可选)
 * @param {String} [params.vendorId] - 查询由该分销商（及其下级）激活的卡 (基于激活券的`original_vendor_chain`) (可选)
 * @param {Number} [params.page] - 页码 (默认1) (可选)
 * @param {Number} [params.pageSize] - 每页数量 (默认10) (可选)
 */
export async function requestCardList(params) {
    const res = await request.get('/api/distro/card/list', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 获取年卡详细信息
 * @param {Object} params - 查询参数
 * @param {String} [params.cardId] - 系统内年卡ID (优先使用) (可选)
 * @param {String} [params.cardNumber] - 年卡卡号 (如果ID未知) (可选)
 */
export async function requestCardDetail(params) {
    const res = await request.get('/api/distro/card/detail', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 内部/回调接口）接收第三方系统的年卡激活信息并同步到系统。(或使用特定内部接口 `/internal/sync/card/activation`)
 * @param {Object} params - 激活参数
 * @param {String} params.cardNumber - 年卡卡号 (必填)
 * @param {String} params.activationTicketNumber - 激活时使用的实体券号 (必填)
 * @param {String} params.activationTime - 激活发生时间 (格式: yyyy-MM-dd HH:mm:ss) (必填)
 * @param {String} [params.thirdPartyUserId] - 第三方用户ID (可选)
 * @param {String} [params.userInfo] - 用户信息 (JSON格式) (可选)
 * @param {String} [params.expiryDate] - 计算出的年卡到期日期 (格式: yyyy-MM-dd) (可选)
 * @param {String} [params.status] - 年卡初始状态 (可选, 默认'Active')
 */
export async function requestCardActivate(params) {
    const res = await request.post('/api/distro/card/sync', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}