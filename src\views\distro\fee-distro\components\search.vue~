<template>
  <ele-card class="search-form">
    <el-form :model="form" :label-width="60" @submit.prevent="handleSearch">
      <el-row :gutter="8">
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="卡号">
            <el-input v-model="form.cardNumber" placeholder="请输入年卡卡号" clearable />
          </el-form-item>
        </el-col>
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="实体券">
            <el-input v-model="form.ticketNumber" placeholder="请输入实体券号" clearable />
          </el-form-item>
        </el-col>
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商">
            <vendor-select v-model="form.vendorId" placeholder="请选择分销商" />
          </el-form-item>
        </el-col>


        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="佣金状态">
            <el-select v-model="form.status" placeholder="请选择状态" clearable>
              <el-option label="待结算" value="PendingSettlement" />
              <el-option label="已结算" value="Settled" />
              <el-option label="已提现" value="IncludedInWithdrawal" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="佣金产生时间">
            <el-date-picker v-model="form.renewalTimeRange" type="daterange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="结算时间">
            <el-date-picker v-model="form.settleTimeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" value-format="YYYY-MM-DD" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :lg="4" :md="12" :sm="12" :xs="24" style="text-align: right">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { ref } from 'vue';

const emit = defineEmits(['search']);

/** 表单数据 */
const form = ref({
  ledgerId: '',
  cardNumber: '',
  ticketNumber: '',
  vendorId: '',
  status: '',
  renewalTimeRange: [],
  settleTimeRange: []
});

/** 搜索 */
const handleSearch = () => {
  const params = {
    ...form.value,
    renewalTimeStart: form.value.renewalTimeRange?.[0],
    renewalTimeEnd: form.value.renewalTimeRange?.[1],
    settleTimeStart: form.value.settleTimeRange?.[0],
    settleTimeEnd: form.value.settleTimeRange?.[1]
  };
  delete params.renewalTimeRange;
  delete params.settleTimeRange;
  emit('search', params);
};

/** 重置 */
const handleReset = () => {
  form.value = {
    ledgerId: '',
    cardNumber: '',
    ticketNumber: '',
    vendorId: '',
    status: '',
    renewalTimeRange: [],
    settleTimeRange: []
  };
  handleSearch();
};
</script>

<style scoped>
.search-form {
  margin-bottom: 16px;
}
</style>
