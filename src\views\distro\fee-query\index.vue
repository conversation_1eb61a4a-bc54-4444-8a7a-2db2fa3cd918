<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="productId" :columns="columns" :datasource="FeeList" :pagination="true"
        :show-overflow-tooltip="false">
        <template #toolbar>
        </template>

        <template #action="{ row }">
          <template v-if="row.username !== 'admin'">
            <el-link v-permission="'system:user:remove'" type="primary" :underline="false" @click="showBatch(row)">
              查看
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 详情内容 - 抽屉 -->
    <Detail :visible="showDetail" @close="onClose" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import Search from './components/search.vue';
import Detail from './components/detail.vue';
import { useUserStore } from '@/store/modules/user'
import { requestFeeLedgerList } from '@/api/distro/fee-query/index';
/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: 'index',
    columnKey: 'index',
    width: 50,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'productId',
    label: '明细ID',
    align: 'center',
    minWidth: 110
  },
  {
    prop: 'ledgerId',
    label: '总账目ID',
    align: 'center',
    minWidth: 200,
  },
  {
    prop: 'triggeredByCardNumber',
    label: '年卡卡号',
    align: 'center',
  },
  {
    prop: 'renewalTime',
    label: '续期时间',
    align: 'center',
  },
  {
    prop: 'feeAmount',
    label: '佣金金额',
    align: 'center',
  },
  {
    prop: 'vendorLevelAtTime',
    label: '层级',
    align: 'center',
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    minWidth: 60,
    slot: 'status'
  },
  {
    prop: 'settlementTime',
    label: '结算时间',
    align: 'center',
    minWidth: 110,
  },
  {
    prop: 'withdrawalRequestId',
    label: '申请ID',
    align: 'center',
    minWidth: 110,
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    minWidth: 110,
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 60,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

const store = useUserStore();

/** 是否显示详情 */
let showDetail = ref(false);

/** 表格数据源 */
const FeeList = ({ pages, where, orders, filters }) => {
  let { roleList } = store.info;
  console.log(roleList.map(k => k.roleKey), '角色key');

  // 分销商
  // requestMyFeeDistroList({
  //   ...pages,
  //   ...where,
  // })

  // 管理员
  return requestFeeLedgerList({
    ...pages,
    ...where,
    ...orders,
    ...filters,
  });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 查看数据 */
const showBatch = (row) => {
  console.log(row, 'show');
  // 获取id
  showDetail.value = true;
}

/** 关闭详情 */
const onClose = () => {
  showDetail.value = false;
}
</script>
