<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="productId" :columns="columns" :datasource="TicketSegmentList"
        :pagination="true" :show-overflow-tooltip="false">
        <template #toolbar>
          <!-- <el-button v-permission="'system:admin:add'" type="primary" class="ele-btn-icon" :icon="PlusOutlined" @click="openCreate()">
            创建
          </el-button> -->
        </template>


        <template #isActive="{ row }">
          <el-tag :type="row.isActive ? 'success' : 'danger'">{{ row.isActive ? '已启用' : '未启用' }}</el-tag>
        </template>

        <template #action="{ row }">
          <template v-if="row.username !== 'admin'">
            <el-link v-permission="'system:user:remove'" type="primary" :underline="false" @click="handleShowDetail(row)">
              查看
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <el-link v-permission="'system:user:remove'" type="primary" :underline="false" @click="openAssign(row)">
              分配
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 创建 -->
    <!-- <Create v-model="showCreate" :data="current" @done="reload" /> -->
    <!-- 分配 -->
    <Assign v-model="showAssign" :data="current" @done="reload" />
    <!-- 详情内容 - 抽屉 -->
    <Detail :visible="showDetail" :data="current" @close="onClose" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
// import {
//   PlusOutlined,
// } from '@/components/icons';
import { EleMessage } from 'ele-admin-plus/es';
import Search from './components/search.vue';
import Assign from './components/assign.vue';
// import Create from './components/create.vue';
import Detail from './components/detail.vue';
import { requestTicketSegmentList } from '@/api/distro/ticket-segment/index';
/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'segmentId',
    label: '号段ID',
    align: 'center',
    hideInTable: true,
    width: 200
  },
  {
    prop: 'productId',
    label: '产品编号',
    align: 'center',
    hideInTable: true,
    width: 200,
  },
  {
    prop: 'productName',
    label: '产品名称',
    align: 'left',
    minWidth: 100,
  },
  {
    prop: 'productGid',
    label: '产品编号',
    align: 'center',
    minWidth: 100,
  },
  {
    prop: 'startNumber',
    label: '起始券号',
    align: 'center',
    minWidth: 100,
  },
  {
    prop: 'endNumber',
    label: '结束券号',
    align: 'center',
    minWidth: 100,
  },
  {
    prop: 'totalCount',
    label: '总券数',
    align: 'center',
  },
  // {
  //   prop: 'assignedToVendorId',
  //   label: '持有者ID',
  //   align: 'center',
  //   minWidth: 100,
  // },
  {
    prop: 'assignedToVendorName',
    label: '持有者名称',
    align: 'center',
    minWidth: 100,
  },
  // {
  //   prop: 'assignmentTime',
  //   label: '分配时间',
  //   align: 'center',
  //   minWidth: 100,
  // },
  {
    prop: 'isActive',
    label: '是否可用',
    align: 'center',
    minWidth: 100,
    slot: 'isActive'
  },
  {
    prop: 'createTime',
    label: '分配时间',
    align: 'center',
    minWidth: 160,
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 120,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示create弹窗 */
// const showCreate = ref(false);
/** 是否显示create弹窗 */
const showAssign = ref(false);

/** 是否显示详情 */
const showDetail = ref(false);

/**  */

/** 表格数据源 */
// const datasource = ({ pages, where }) => {
//   pageUsers({ ...where, ...pages }).then(res => {
//     console.log(res, 'W');
//   })

//   return pageUsers({ ...where, ...pages });
// };

/** 表格数据源 */
const TicketSegmentList = ({ pages, where }) => {
  return requestTicketSegmentList({
    ...pages, ...where,
  });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 打开编辑弹窗 */
// const openCreate = () => {
//   showCreate.value = true;
// };

/** 打开分配弹窗 */
const openAssign = (row) => {
  let { segmentId } = row;
  if (!segmentId) {
    EleMessage.error('号段ID为空');
    return;
  }
  current.value = row;
  showAssign.value = true;
};

/** 查看数据 */
const handleShowDetail = (row) => {
  let { segmentId } = row;
  if (!segmentId) {
    EleMessage.error('号段ID为空');
    return;
  }
  current.value = row;
  // 获取id
  showDetail.value = true;
}

/** 关闭详情 */
const onClose = () => {
  showDetail.value = false;
}
</script>
