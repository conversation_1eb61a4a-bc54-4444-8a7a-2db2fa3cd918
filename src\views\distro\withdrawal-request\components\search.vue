<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="60px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">   
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="状态">
            <dict-data type="select" code="withdrawalRequestStatus" v-model="form.status" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商">
            <vendor-select v-model="form.vendorId" />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { onMounted } from "vue";
import { useFormData } from "@/utils/use-form-data";
import VendorSelect from "@/components/VendorSelect/index.vue";

/** 定义事件 */
const emit = defineEmits(["search"]);

/** 表单数据 */
const [form, resetFields] = useFormData({
  username: null,
  realName: null,
  roleId: null,
  teamId: null
});

/** 搜索 */
const search = () => {
  emit("search", { ...form });
};

/** 重置 */
const reset = () => {
  resetFields();
  search();
};


/** 更多 */
// const more = ref(false);
// const toggleMore = () => {
//   more.value = !more.value;
// };

onMounted(() => {

});
</script>
