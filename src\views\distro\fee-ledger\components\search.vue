<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="60px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="卡号">
            <el-input clearable v-model.trim="form.cardNumber" placeholder="请输入卡号" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="券号">
            <el-input clearable v-model.trim="form.ticketNumber" placeholder="请输入券号" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商">
            <vendor-select v-model="form.vendorId" placeholder="请选择分销商" />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="120px" label="计算时间">
            <date-range-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleDateRangeChange"
            />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { ref } from 'vue';
import { useFormData } from '@/utils/use-form-data';
import DateRangePicker from '@/components/DateRangePicker/index.vue';
import VendorSelect from '@/components/VendorSelect/index.vue';
import dayjs from 'dayjs';

const today = dayjs()
const day2Before = today.subtract(2, 'day')

/** 定义事件 */
const emit = defineEmits(['search']);

/** 日期范围 */
const dateRange = ref([day2Before, today]);

/** 表单数据 */
const [form, resetFields] = useFormData({
  cardNumber: null,
  ticketNumber: null,
  vendorId: null,
  calculationTimeStart: null,
  calculationTimeEnd: null,
});

/** 处理日期范围变化 */
const handleDateRangeChange = (val) => {
  form.calculationTimeStart = val?.[0] || null;
  form.calculationTimeEnd = val?.[1] || null;
};

/** 搜索 */
const search = () => {
  emit('search', { ...form });
};

/** 重置 */
const reset = () => {
  dateRange.value = [day2Before, today];
  resetFields();
  search();
};
</script> 