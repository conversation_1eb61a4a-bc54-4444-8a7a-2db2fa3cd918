<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="72px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="状态">
            <el-select clearable v-model="form.state" placeholder="请选择" class="ele-fluid">
              <el-option label="待处理" :value="0" />
              <el-option label="成功" :value="1" />
              <el-option label="处理中" :value="2" />
              <el-option label="失败" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="文件名">
            <el-input clearable v-model.trim="form.fileName" placeholder="请输入文件名" />
          </el-form-item>
        </el-col>

        <el-col v-if="more" :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="创建时间">
            <el-date-picker v-model="form.createTime" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" class="ele-fluid" />
          </el-form-item>
        </el-col>
      
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-link type="primary" :underline="false" @click="toggleMore">
              <span class="mx-2">更多</span>
              <el-icon v-if="!more">
                <IconElArrowDown />
              </el-icon>
              <el-icon v-else>
                <IconElArrowUp />
              </el-icon>
            </el-link>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { ref } from 'vue';
import { useFormData } from '@/utils/use-form-data';

/** 定义事件 */
const emit = defineEmits(['search']);

/** 表单数据 */
const [form, resetFields] = useFormData({
  sort: 'updateTime',
  order: 'desc',
  fileName: null,
  fileType: null,
  state: null,
  createTime: null
});

/** 搜索 */
const search = () => {
  emit('search', { ...form });
};

/** 重置 */
const reset = () => {
  resetFields();
  search();
};

/** 更多 */
const more = ref(false);
const toggleMore = () => {
  more.value = !more.value;
};
</script>