---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

本文档描述了项目的主要结构和重要文件。

## 主要入口文件
- [index.html](mdc:index.html) - 项目的HTML入口文件
- [vite.config.js](mdc:vite.config.js) - Vite配置文件
- [src/](mdc:src) - 源代码目录

## 配置文件
- [package.json](mdc:package.json) - 项目依赖和脚本配置
- [jsconfig.json](mdc:jsconfig.json) - JavaScript配置
- [.eslintrc.json](mdc:.eslintrc.json) - ESLint规则配置
- [prettier.config.js](mdc:prettier.config.js) - Prettier代码格式化配置
- [postcss.config.js](mdc:postcss.config.js) - PostCSS配置

## 目录结构
- `src/` - 源代码目录
- `public/` - 静态资源目录
- `docker/` - Docker相关配置
- `node_modules/` - 项目依赖

## 开发规范
1. 使用ESLint进行代码质量控制
2. 使用Prettier进行代码格式化
3. 遵循.editorconfig中定义的编码规范

## 注意事项
- 请确保遵循.gitignore和.prettierignore中定义的忽略规则
- 代码提交前应当进行lint检查和格式化

