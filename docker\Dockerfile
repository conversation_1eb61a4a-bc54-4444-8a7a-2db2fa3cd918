FROM nginx:1.26.2-alpine

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 修改时区
RUN apk update && apk add bash curl vim ca-certificates tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    mkdir -p /etc/nginx/logs

COPY nginx.conf /etc/nginx/nginx.conf
COPY front-app.nginx /etc/nginx/templates/default.conf.template

ADD html/ /usr/share/nginx/html/

#COPY ./run.sh /docker-entrypoint.sh

#RUN chmod a+x /docker-entrypoint.sh

#ENTRYPOINT ["/docker-entrypoint.sh"]

# 每次容器启动时执行
CMD ["nginx", "-g", "daemon off;"]

# 容器应用端口
EXPOSE 80

#EXPOSE 443
