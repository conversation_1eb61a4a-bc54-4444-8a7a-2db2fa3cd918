<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="60px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="券号">
            <el-input clearable v-model.trim="form.ticketNumber" placeholder="请输入编号" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="产品">
            <product-select v-model="form.productId" placeholder="请选择产品" />
          </el-form-item>
        </el-col>

        <el-col v-if="more" :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="120px" label="激活时间">
            <el-date-picker v-model="form.activationTime" type="datetimerange" start-placeholder="起始时间"
              end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd"
              time-format="A hh:mm:ss" />
          </el-form-item>
        </el-col>

        <el-col v-if="more" :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商">
            <el-input clearable v-model.trim="form.vendorId" placeholder="请输入ID" />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-link type="primary" :underline="false" @click="toggleMore">
              <span class="mx-2">更多</span>
              <el-icon v-if="!more">
                <IconElArrowDown />
              </el-icon>
              <el-icon v-else>
                <IconElArrowUp />
              </el-icon>
            </el-link>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import Dayjs from "dayjs";
import { ref } from "vue";
import { useFormData } from "@/utils/use-form-data";
import ProductSelect from "@/components/ProductSelect/index.vue";

/** 定义事件 */
const emit = defineEmits(["search"]);

/** 表单数据 */
const [form, resetFields] = useFormData({
  ticketNumber: null,
  productId: null,
  currentSegmentId: null,
  status: null,
  vendorId: null,
  activationTime: '',
});

/** 搜索 */
const search = () => {
  let activationTimeStart = null;
  let activationTimeEnd = null;

  if (form.activationTime && form.activationTime.length === 2) {
    activationTimeStart = Dayjs(form.activationTime[0]).format('YYYY-MM-DD HH:mm:ss');
    activationTimeEnd = Dayjs(form.activationTime[1]).format('YYYY-MM-DD HH:mm:ss');
  }

  emit("search", { ...form, activationTimeStart, activationTimeEnd });
};

/** 更多 */
const more = ref(false);
const toggleMore = () => {
  more.value = !more.value;
};

/** 重置 */
const reset = () => {
  resetFields();
  search();
};
</script>
