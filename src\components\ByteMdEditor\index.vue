<!-- markdown编辑器 -->
<template>
  <div v-show="visible" ref="rootRef" class="ele-bytemd-wrap"></div>
</template>

<script setup>
  import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
  import { Editor } from 'bytemd';
  import 'bytemd/dist/index.min.css';

  defineOptions({ name: 'ByteMdEditor' });

  const props = defineProps({
    /** 绑定值 */
    modelValue: {
      type: String,
      required: true,
      default: ''
    },
    /** 编辑器配置 */
    config: Object,
    /** 高度 */
    height: String,
    /** 全屏时的层级 */
    fullIndex: {
      type: Number,
      default: 999
    }
  });

  const emit = defineEmits(['update:modelValue', 'change']);

  /** 编辑器实例 */
  let editor = null;

  /** 根节点 */
  const rootRef = ref(null);

  /** 是否可见 */
  const visible = ref(false);

  /** 初始化编辑器 */
  const initEditor = async () => {
    if (!rootRef.value) return;
    
    // 等待 DOM 更新
    await nextTick();
    
    // 如果已经存在编辑器实例，先销毁
    if (editor) {
      editor.$destroy?.();
      editor = null;
    }
    
    // 创建新的编辑器实例
    const ins = new Editor({
      target: rootRef.value,
      props: {
        ...props.config,
        value: props.modelValue || ''
      }
    });
    
    ins.$on('change', (e) => {
      emit('update:modelValue', e.detail.value);
      emit('change', e.detail.value);
    });
    
    editor = ins;
  };

  // 渲染编辑器
  onMounted(async () => {
    // 先设置为不可见
    visible.value = false;
    // 等待 DOM 更新
    await nextTick();
    // 设置为可见，触发初始化
    visible.value = true;
  });

  // 组件销毁前清理
  onBeforeUnmount(() => {
    if (editor) {
      editor.$destroy?.();
      editor = null;
    }
  });

  // 监听可见性变化
  watch(visible, async (val) => {
    if (val) {
      await initEditor();
    }
  });

  // 更新配置
  watch(
    () => props.config,
    async (config) => {
      if (!editor || !visible.value) return;
      
      const option = { ...config };
      Object.keys(option).forEach((key) => {
        if (typeof option[key] === 'undefined') {
          delete option[key];
        }
      });
      
      await nextTick();
      editor.$set?.(option);
    },
    { deep: true }
  );

  // 更新值
  watch(
    () => props.modelValue,
    async (value) => {
      if (!editor || !visible.value) return;
      await nextTick();
      editor.$set?.({ value: value || '' });
    }
  );

  defineExpose({ editor });
</script>

<style lang="scss" scoped>
  .ele-bytemd-wrap {
    width: 100%;
    line-height: initial;
  }

  /* 修改编辑器高度 */
  .ele-bytemd-wrap :deep(.bytemd) {
    height: v-bind(height);

    /* 修改全屏的zIndex */
    &.bytemd-fullscreen {
      z-index: v-bind(fullIndex);
    }

    /* 去掉默认的最大宽度限制 */
    .CodeMirror .CodeMirror-lines {
      max-width: 100%;
    }

    pre.CodeMirror-line,
    pre.CodeMirror-line-like {
      padding: 0 24px;
    }

    .markdown-body {
      max-width: 100%;
      padding: 16px 24px;
    }

    /* 去掉github图标 */
    .bytemd-toolbar-right > .bytemd-toolbar-icon:last-child {
      display: none;
    }
  }
</style>
