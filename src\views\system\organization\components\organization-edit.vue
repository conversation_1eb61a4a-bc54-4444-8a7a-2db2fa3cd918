<!-- 机构编辑弹窗 -->
<template>
  <ele-modal form :width="620" v-model="visible" :title="isUpdate ? '修改机构' : '添加机构'" @open="handleOpen">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top" @submit.prevent="">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="机构类型" prop="orgType">
            <dict-data code="orgType" v-model="form.orgType" type="text" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="上一级机构" >
            {{ form.parentName }}
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="机构名称" prop="orgName">
            <el-input clearable :maxlength="20" v-model="form.orgName" placeholder="请输入机构名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构代码" prop="treeCode">
            <el-input clearable :maxlength="20" v-model="form.treeCode" placeholder="请输入机构代码" />
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import {
  addOrganization,
  updateOrganization
} from '@/api/system/organization';

const props = defineProps({
  /** 修改或添加时的数据 */
  data: Object,
});

const emit = defineEmits(['done']);

/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean });

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  parentId: void 0,
  orgName: '',
  treeCode: '',
  orgType: void 0,
  tyOrgId: '',
  comments: ''
});

/** 表单验证规则 */
const rules = reactive({
  orgName: [
    {
      required: true,
      message: '请输入机构名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  orgType: [
    {
      required: true,
      message: '请选择机构类型',
      trigger: 'change'
    }
  ],
  treeCode: [
    {
      validator: (rule, value, callback) => {
        if (String(form.orgType) === '2' && !value) {
          callback(new Error('请输入机构代码'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
});

/** 关闭弹窗 */
const handleCancel = () => {
  visible.value = false;
};

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    const saveOrUpdate = isUpdate.value
      ? updateOrganization
      : addOrganization;
    saveOrUpdate({ ...form, parentId: form.parentId || 0 })
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

/** 弹窗打开事件 */
const handleOpen = () => {
  const data = props.data || {};
  assignFields({
      ...data,      
  });
  isUpdate.value = data.id;

  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  });
};
</script>
