<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="logId" :columns="columns" :datasource="serviceLogList" :pagination="true"
        :show-overflow-tooltip="false">
        <template #toolbar>
          <!-- 业务日志不需要新建按钮 -->
        </template>

        <template #ticketNumber="{ row }">
          <el-link v-if="row.ticketNumber" type="primary" :underline="false" @click="showDetail(row)">
            {{ row.ticketNumber }}
          </el-link>
          <div v-else class="flex-y">
            <span>{{ row.startNum }}</span>
            <span>{{ row.endNum }}</span>
          </div>
        </template>

        <template #serviceType="{ row }">
          <dict-data type="text" code="serviceType" v-model="row.serviceType" />
        </template>

        <template #status="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
        </template>

        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="showDetail(row)">
            查看
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 详情内容 - 抽屉 -->
    <Detail :visible="showDetailDrawer" :data="current" @close="onClose" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import Search from './components/search.vue';
import Detail from './components/detail.vue';
import { requestServiceLogList } from '@/api/distro/service-log/index';
import { todayStr } from '@/utils/common';

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'logId',
    label: '日志ID',
    align: 'center',
    hideInTable: true,
    width: 200,
  },
  {
    prop: 'ticketNumber',
    label: '实体券',
    align: 'left',
    width: 200,
    slot: 'ticketNumber'
  },  
  {
    prop: 'ticketCount',
    label: '数量',
    align: 'center',
    width: 60
  },
  {
    prop: 'cardNumber',
    label: '年卡',
    align: 'center',
    width: 180
  },
  {
    prop: 'serviceType',
    label: '类型',
    align: 'left',
    width: 100,
    slot: 'serviceType'
  },
  {
    prop: 'description',
    label: '描述',
    align: 'left',
    minWidth: 200
  },
  {
    prop: 'remark',
    label: '备注',
    align: 'left',
    minWidth: 90
  },
  {
    prop: 'vendorName',
    label: '分销商',
    align: 'center',
    width: 120
  },
  {
    prop: 'createByUsername',
    label: '操作员',
    align: 'center',
    width: 100
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    minWidth: 80,
    slot: 'status',
    filterMultiple: false,
    hideInTable: true,
    filters: [
      {
        text: '正常',
        value: '0'
      },
      {
        text: '异常',
        value: '1'
      }
    ],
  },
  {
    prop: 'createTime',
    label: '日志时间',
    sortable: 'custom',
    align: 'center',
    minWidth: 160,
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 100,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 当前查看的数据 */
const current = ref(null);

/** 是否显示详情抽屉 */
const showDetailDrawer = ref(false);

/** 表格数据源 */
const serviceLogList = ({ pages, where, orders, filters }) => {
  if (!where.startTime) {
    where.startTime = todayStr();
  }
  if (!where.endTime) {
    where.endTime = todayStr();
  }

  return requestServiceLogList({ ...where, ...pages, ...orders, ...filters });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 获取状态显示文本 */
const getStatusText = (status) => {
  const statusMap = {
    '0': '正常',
    '1': '异常'
  };
  return statusMap[status] || status;
};

/** 获取状态标签类型 */
const getStatusType = (status) => {
  const typeMap = {
    '0': 'success',
    '1': 'danger'
  };
  return typeMap[status] || 'info';
};

/** 查看详情 */
const showDetail = (row) => {
  current.value = row;
  showDetailDrawer.value = true;
};

/** 关闭详情 */
const onClose = () => {
  showDetailDrawer.value = false;
};
</script> 