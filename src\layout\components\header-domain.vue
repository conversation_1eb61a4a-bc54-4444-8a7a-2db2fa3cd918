<!-- 域选择器组件 -->
<template>
  <ele-popover
    :width="200"
    trigger="click"
    transition="el-zoom-in-top"
    :content-style="{ padding: 0 }"
    :body-style="{ overflow: 'hidden' }"
    :popper-options="{
      strategy: 'fixed',
      modifiers: [{ name: 'offset', options: { offset: [0, 5] } }]
    }"
  >
    <template #reference>
      <div style="display: flex; align-items: center; height: 100%">
        <span class="domain-text">{{ activeDomain.name }}</span>
        <el-icon :size="12" style="margin-left: 4px"><ArrowDown /></el-icon>
      </div>
    </template>
    
    <div class="domain-menu">
      <!-- 当域数量超过5个时显示筛选输入框 -->
      <div v-if="showSearchInput" class="domain-search">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索域名"
          clearable
          size="small"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      
      <div 
        v-for="item in filteredDomains" 
        :key="item.id"
        class="domain-item"
        :class="{ active: item.id === activeDomain.id }"
        @click="handleDomainChange(item)"
      >
        {{ item.name }}
      </div>
      
      <!-- 无匹配结果时显示 -->
      <div v-if="filteredDomains.length === 0" class="domain-empty">
        无匹配域名
      </div>
    </div>
  </ele-popover>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ArrowDown } from '@/components/icons';
import { Search } from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { EleMessage } from 'ele-admin-plus/es';

// 用户状态
const userStore = useUserStore();

// 所有可用域
const domains = computed(() => userStore.info?.domainList || []);

// 搜索关键词
const searchKeyword = ref('');

// 是否显示搜索输入框（当域数量超过5个时）
const showSearchInput = computed(() => domains.value.length > 5);

// 域选项
const domainOptions = computed(() => {
  return domains.value.map(domain => ({
    ...domain
  }));
});

// 过滤后的域列表
const filteredDomains = computed(() => {
  if (!searchKeyword.value) {
    return domainOptions.value;
  }
  
  const keyword = searchKeyword.value.toLowerCase();
  return domainOptions.value.filter(domain => 
    domain.name.toLowerCase().includes(keyword) || 
    (domain.id && String(domain.id).toLowerCase().includes(keyword))
  );
});

// 当前激活的域
const activeDomain = computed(() => {
  const currentDomainId = userStore.currentDomainId;
  const found = domains.value.find(d => d.id === currentDomainId);
  
  if (found) {
    return found;
  }
  
  // 如果没找到，使用第一个域或默认值
  return domains.value.length > 0 
    ? domains.value[0]
    : { id: null, name: '默认域' };
});

// 初始化时设置默认域
onMounted(() => {
  if (domains.value.length > 0 && !userStore.currentDomainId) {
    userStore.setCurrentDomain(domains.value[0].id);
  }
});

// 处理域切换
const handleDomainChange = (domain) => {
  if (domain.id !== userStore.currentDomainId) {
    userStore.setCurrentDomain(domain.id);
    EleMessage.success(`已切换到域: ${domain.name}`);
  }
};
</script>

<style lang="scss" scoped>
.domain-text {
  font-size: 14px;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.domain-menu {
  padding: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.domain-search {
  padding: 0 0 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  margin-bottom: 8px;
}

.domain-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 4px;
  margin-bottom: 4px;
  
  &:hover {
    background-color: hsla(0, 0%, 60%, 0.08);
  }
  
  &.active {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    font-weight: 500;
  }
}

.domain-empty {
  padding: 12px;
  text-align: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}
</style>

<style>
/* 全局样式 */
.domain-popover {
  padding: 8px;
}
</style> 