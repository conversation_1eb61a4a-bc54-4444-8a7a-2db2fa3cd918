<template>
  <el-tree-select
    :model-value="modelValue"
    @update:model-value="handleUpdate"
    :data="vendorList"
    :placeholder="placeholder"
    check-strictly
    filterable
    clearable 
    :render-after-expand="false"   
    v-bind="$attrs"
  >
    <template #empty>
      <el-empty description="暂无数据" :image-size="32" />
    </template>
  </el-tree-select>
</template>

<script setup>
import {ref, watch, onMounted} from 'vue';
import {requestVendorList} from '@/api/distro/vendor/index';
import {eachTree, toTree} from 'ele-admin-plus/es';
import {debounce} from 'lodash';

const props = defineProps({
  modelValue: [String, Number, null],
  parentVendorId: [String, null],
  childType: [String, null],    // direct: 只查询直接下级 all: 查询所有下级，包括下级的下级
  level: [Number, null],        // 目标分销商的级别
  placeholder: {
    type: String,
    default: '请选择分销商'
  }
});
const emit = defineEmits(['update:modelValue']);

const vendorList = ref([]);

const fetchVendorList = async () => {
  const result = await requestVendorList({pageNo: 1, 
    pageSize: 9999, 
    parentVendorId: props.parentVendorId, 
    childType: props.childType,
    level: props.level
  });
  const treeData = toTree({
    data: result.rows.map(child => {
      child.label = child.name;
      child.value = child.vendorId;
      return child;
    }),
    idField: 'vendorId',
    parentIdField: 'parentVendorId',
  });
  eachTree(treeData, (node) => {
    node.hasChildren = node.children?.length > 0;
  });

  vendorList.value = treeData;
};

const debounceFetchVendorList = debounce(fetchVendorList, 300);

onMounted(debounceFetchVendorList);

const handleUpdate = (val) => {
  emit('update:modelValue', val);
};

watch(
  () => props.modelValue,
  val => emit('update:modelValue', val)
);

// 监听 parentVendorId childType 和 level 变化
watch(
  [() => props.parentVendorId, () => props.childType, () => props.level],
  () => {
    debounceFetchVendorList();
  }
);
</script>
