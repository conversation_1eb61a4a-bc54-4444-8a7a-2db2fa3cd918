<!-- 编辑弹窗 -->
<template>
  <ele-modal form :width="560" v-model="visible" title="分配实体券号段" @open="handleOpen" destroy-on-close>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" @submit.prevent="">
      <el-form-item label="分配号段" prop="sourceSegmentId">
        <el-input clearable disabled v-model="form.sourceSegmentId" placeholder="请输入源号段ID" />
      </el-form-item>
      <el-form-item label="分配目标" prop="targetVendorId">
        <el-tree-select placeholder="请输入分配目标下级分销商ID" v-model="form.targetVendorId" :data="vendorList" check-strictly
          :render-after-expand="false" />
      </el-form-item>
      <el-form-item label="起始券号" prop="assignStartNumber">
        <el-input clearable v-model="form.assignStartNumber" placeholder="请输入分配子号段起始券号" />
      </el-form-item>
      <el-form-item label="结束券号" prop="assignEndNumber">
        <el-input clearable v-model="form.assignEndNumber" placeholder="请输入分配子号段结束券号" />
      </el-form-item>
      <el-form-item label="总数" prop="totalCount">
        <el-input clearable v-model.number="form.totalCount" placeholder="请输入总数" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input clearable v-model="form.description" resize="none" type="textarea" :rows="3" placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        分配
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { EleMessage, toTree } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import { requestVendorList } from '@/api/distro/vendor/index';
import { requestTicketSegmentAssign } from '@/api/distro/ticket-segment/index';

/** 修改回显的数据 */
const props = defineProps({
  data: {
    type: Object,
    default: () => { },
  }
});

/** 定义事件 */
const emit = defineEmits(['update:modelValue', 'done']);

/** 弹窗是否打开 */
const visible = defineModel('modelValue');

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  sourceSegmentId: null,
  assignStartNumber: null,
  assignEndNumber: null,
  description: null,
  totalCount: 0,
  targetVendorId: null
});

/** 表单验证规则 */
const rules = reactive({
  sourceSegmentId: [
    {
      required: true,
      message: '源号段ID不能为空',
      type: 'string',
      trigger: 'blur'
    }
  ],
  assignStartNumber: [
    {
      required: true,
      message: '分配号段起始券号不能为空',
      type: 'string',
      trigger: 'blur'
    }
  ],
  assignEndNumber: [
    {
      required: true,
      message: '分配号段结束券号不能为空',
      type: 'string',
      trigger: 'blur'
    }
  ],
  targetVendorId: [
    {
      required: true,
      message: '分配目标下级分销商ID不能为空',
      type: 'string',
      trigger: 'blur'
    }
  ],
  description: [
    {
      max: 20,
      message: '描述长度不能超过20个字符',
      trigger: 'change'
    }
  ]
});

const vendorList = ref([]);

/** 关闭弹窗 */
const handleCancel = () => {
  emit('update:modelValue', false);
};

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }

    loading.value = true;

    if (form.startNumber > form.endNumber) {
      EleMessage.error('起始券号不能大于结束券号');
      return;
    }
    // 总数校验
    // if (form.totalCount !== (form.endNumber - form.startNumber + 1)) {
    //   EleMessage.error('总数与实际券号段不匹配');
    //   return;
    // }

    const saveOrUpdate = requestTicketSegmentAssign;
    // 如果有密码字段且不为空，则加密
    const params = {
      ...form
    };

    saveOrUpdate(params)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

/** 弹窗打开事件 */
const handleOpen = async () => {
  resetFields();
  let { assignedToVendorId } = props.data; // 当前持有者id 分销商ID
  console.log(assignedToVendorId, 'props.id');

  if (!assignedToVendorId) {
    // 管理员 
    let { rows } = await requestVendorList({ ...{ pageNo: 1, pageSize: 10000, level: 1 } })
    vendorList.value = toTree({
      data: rows.map(child => {
        child.label = child.name; // 假设 name 是分销商名称字段名
        child.value = child.vendorId; // 假设 vendorId 是分销商ID字段名
        return child
      }), // 假设 rows 是你的分销商数据数组
      idField: 'vendorId', // 分销商ID字段名
      parentIdField: 'parentVendorId' // 上级分销商ID字段名
    }); // 保存分销商数据
  } else {
    // 分销商分配 获取到分销商id ，查询下级分销商

    // 请求分销商数据
    let { rows } = await requestVendorList({ ...{ pageNo: 1, pageSize: 10000, parentVendorId: assignedToVendorId } })
    vendorList.value = toTree({
      data: rows.map(child => {
        child.label = child.name; // 假设 name 是分销商名称字段名
        child.value = child.vendorId; // 假设 vendorId 是分销商ID字段名
        return child
      }), // 假设 rows 是你的分销商数据数组
      idField: 'vendorId', // 分销商ID字段名
      parentIdField: 'parentVendorId' // 上级分销商ID字段名
    }); // 保存分销商数据
  }


  const params = Object.assign({}, props.data);
  params.sourceSegmentId = props.data.segmentId;
  params.assignStartNumber = props.data.startNumber;
  params.assignEndNumber = props.data.endNumber;
  params.description = '';
  assignFields(params);

  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  });
};
</script>
