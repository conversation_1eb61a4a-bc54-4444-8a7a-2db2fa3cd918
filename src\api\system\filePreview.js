import request, { handleResponse } from '@/utils/request';

const baseURL = '/api/system/file-preview';

/**
 * 分页查询文件预览列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>}
 */
export function getFilePreviewList(params) {
  return handleResponse(request.get(baseURL + `/list`, { params }));
}

/**
 * 根据id查询文件预览详情
 * @param {string} id 文件预览id
 * @returns {Promise<Object>}
 */
export function getFilePreview(id) {
  return handleResponse(request.get(baseURL + `/detail?id=${id}`));
}

/**
 * 修改文件预览
 * @param {Object} data 文件预览数据
 * @returns {Promise<void>}
 */
export function updateFilePreview(data) {
  return handleResponse(request.post(baseURL + `/update`, data));
}

/**
 * 删除文件预览
 * @param {string} id 文件预览id
 * @returns {Promise<void>}
 */
export function removeFilePreview(id) {
  return handleResponse(request.post(baseURL + `/delete`, { id }));
}

/**
 * 批量删除文件预览
 * @param {string[]} ids 文件预览id集合
 * @returns {Promise<void>}
 */
export function removeFilePreviews(idList) {
  return handleResponse(request.post(baseURL + `/delete`, { idList }));
}
