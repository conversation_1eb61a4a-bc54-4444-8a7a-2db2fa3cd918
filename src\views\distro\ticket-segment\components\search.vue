<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="60px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="产品">
            <ProductSelect v-model="form.productId" placeholder="请选择产品" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商">
            <VendorSelect v-model="form.assignedToVendorId" placeholder="请选择分销商" />
          </el-form-item>
        </el-col>  

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="券号">
            <el-input clearable v-model.trim="form.ticketNumber" placeholder="请输入券号" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <!-- <el-link type="primary" :underline="false" @click="toggleMore">
              <span class="mx-2">更多</span>
              <el-icon v-if="!more">
                <IconElArrowDown />
              </el-icon>
              <el-icon v-else>
                <IconElArrowUp />
              </el-icon>
            </el-link> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { useFormData } from "@/utils/use-form-data";
import ProductSelect from "@/components/ProductSelect/index.vue";
import VendorSelect from "@/components/VendorSelect/index.vue";

/** 定义事件 */
const emit = defineEmits(["search"]);

/** 表单数据 */
const [form, resetFields] = useFormData({
  productId: null,
  assignedToVendorId: null,
  parentSegmentId: null,
  startNumber: null,
  endNumber: null,
  isActive: null,
});

/** 搜索 */
const search = () => {
  emit("search", { ...form });
};

/** 重置 */
const reset = () => {
  resetFields();
  search();
};

</script>
