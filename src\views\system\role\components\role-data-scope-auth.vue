<!-- 数据权限分配弹窗 -->
<template>
  <ele-modal :width="460" title="分配数据权限" position="center" v-model="visible"
    :body-style="{ padding: '12px 0 12px 22px' }" @open="handleOpen">
    <ele-loading :loading="authLoading" :spinner-style="{ background: 'transparent' }" :style="{
      paddingRight: '20px',
      height: 'calc(100vh - 192px)',
      maxHeight: 'calc(100dvh - 192px)',
      minHeight: '100px',
      overflow: 'auto'
    }">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" @submit.prevent="">
        <el-form-item label="数据权限">
          <el-input v-model="form.dataScope" placeholder="请输入数据权限" />
        </el-form-item>
        <el-form-item label="数据权限">
          <el-input v-model="form.dataScope" placeholder="请输入数据权限" />

          <div class="flex-x gap-1 mb-2">
            <el-button size="small" @click="handleExpandAll(true)">全部展开</el-button>
            <el-button size="small" @click="handleExpandAll(false)">全部折叠</el-button>
            <el-button size="small" @click="handleCheckAll(true)">全选</el-button>
            <el-button size="small" @click="handleCheckAll(false)">取消全选</el-button>
          </div>
          <el-tree ref="treeRef" show-checkbox :data="authData" node-key="id" :default-expand-all="false"
            :props="{ label: 'title' }" :default-checked-keys="checkedKeys" :default-expanded-keys="expandedKeys"
            :check-strictly="true" :style="{ '--ele-tree-item-height': '28px' }">
            <template #default="scope">
              <div>
                <el-icon v-if="scope.data.icon" :size="16" style="margin-right: 6px; vertical-align: -5px">
                  <component :is="scope.data.icon" />
                </el-icon>
                <span style="vertical-align: -2px">{{ scope.data.title }}</span>
              </div>
            </template>
          </el-tree>
        </el-form-item>
      </el-form>
    </ele-loading>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { EleMessage, toTree } from 'ele-admin-plus/es';
import { updateRoleMenus, getRoleMenuIdList } from '@/api/system/role';
import { getMenuTree } from '@/api/system/menu';

const props = defineProps({
  /** 当前角色数据 */
  data: Object
});

/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean });

/** 树组件实例 */
const treeRef = ref(null);

/** 权限数据 */
const authData = ref([]);

/** 权限数据请求状态 */
const authLoading = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 角色权限选中的keys */
const checkedKeys = ref([]);

/** 展开的节点 */
const expandedKeys = ref([]);

/** 查询权限数据 */
const query = async () => {
  authData.value = [];
  checkedKeys.value = [];
  if (!props.data) {
    return;
  }

  try {
    authLoading.value = true;

    const roleId = props.data.id;
    const menuData = await getMenuTree();
    authData.value = toTree({
      data: menuData,
      idField: 'id',
      parentIdField: 'parentId'
    });

    // 回显选中的数据
    const roleMenuIdList = await getRoleMenuIdList(roleId);
    nextTick(() => {
      checkedKeys.value = roleMenuIdList;
    });
  } catch (e) {
    EleMessage.error(e.message);
  } finally {
    authLoading.value = false;
  }

};

/** 关闭弹窗 */
const handleCancel = () => {
  visible.value = false;
};

/** 保存权限分配 */
const save = () => {
  loading.value = true;
  const ids =
    (treeRef.value?.getCheckedKeys?.() ?? []).concat([]
      // treeRef.value?.getHalfCheckedKeys?.() ?? []
    ) ?? [];
  updateRoleMenus(props.data?.id, ids)
    .then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      handleCancel();
    })
    .catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
};

/** 弹窗打开事件 */
const handleOpen = () => {
  query();
};

/** 展开/折叠所有节点 */
const handleExpandAll = (expand) => {
  const tree = treeRef.value;
  if (!tree) {
    return;
  }

  for (const key in tree.store.nodesMap) {
    tree.store.nodesMap[key].expanded = expand;
  }
};

/** 全选/取消全选 */
const handleCheckAll = (checked) => {
  const tree = treeRef.value;
  if (!tree) {
    return;
  }

  for (const key in tree.store.nodesMap) {
    tree.store.nodesMap[key].checked = checked;
  }
};
</script>
