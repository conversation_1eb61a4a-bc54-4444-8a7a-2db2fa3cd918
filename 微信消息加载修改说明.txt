# 如何修改bootstrap.ahk以从文件加载微信图标文本

请按照以下步骤修改您的bootstrap.ahk文件：

1. 在bootstrap.ahk文件中找到以下代码段（大约在第573行）：

```ahk
; alt + w，显示微信消息（查找并点击右下角的微信图标，然后将鼠标还原到点击前的位置）
!w::
Text:="|<>*110$30.zzzzzzzzzzzsDzzzU1zzy00zzy00Tzw88DzsA8Dzs00Dzs001zs000Tw000Dw000Dy0007z0007z0007zA007zzU0Dzzk0Dzzs0Tzzw0TzzzzTzzzzzzzzzzzzzzzU"

Loop, 5
{
    if (ok:=FindText(2197-150000, 1406-150000, 2197+150000, 1406+150000, 0, 0, Text))
    {
        CoordMode, Mouse
        X:=ok.1.x, Y:=ok.1.y, Comment:=ok.1.id
        MouseGetPos, nowX, nowY   
        MouseClick, , %X%, %Y%
        MouseMove , %nowX%, %nowY%, 0
        break
    }
    else
    {
        Sleep, 150
    }
}

return
```

2. 将上述代码替换为以下内容：

```ahk
; alt + w，显示微信消息（从 e:/wechat.txt 加载微信图标的文本描述，然后查找并点击它）
!w::
; 从文件加载微信图标的文本描述
FileRead, WechatIconText, e:/wechat.txt
if ErrorLevel
{
    MsgBox, 0, 错误, 无法读取微信图标描述文件 e:/wechat.txt！
    return
}

Loop, 5
{
    if (ok:=FindText(2197-150000, 1406-150000, 2197+150000, 1406+150000, 0, 0, WechatIconText))
    {
        CoordMode, Mouse
        X:=ok.1.x, Y:=ok.1.y, Comment:=ok.1.id
        MouseGetPos, nowX, nowY   
        MouseClick, , %X%, %Y%
        MouseMove , %nowX%, %nowY%, 0
        break
    }
    else
    {
        Sleep, 150
    }
}

return
```

3. 创建一个名为 e:/wechat.txt 的文件，并将以下内容复制到该文件中（这是原始的微信图标文本描述）：

```
|<>*110$30.zzzzzzzzzzzsDzzzU1zzy00zzy00Tzw88DzsA8Dzs00Dzs001zs000Tw000Dw000Dy0007z0007z0007zA007zzU0Dzzk0Dzzs0Tzzw0TzzzzTzzzzzzzzzzzzzzzU
```

这样，您的脚本将从 e:/wechat.txt 文件中读取微信图标的文本描述，而不是硬编码在脚本中。

4. 如果您想使用不同的微信图标描述，只需更新 e:/wechat.txt 文件，而不需要修改脚本。

注意：
- 请确保路径 e:/wechat.txt 是正确的，并且该文件具有读取权限
- 如果您的系统使用不同的驱动器盘符，请相应地调整路径 