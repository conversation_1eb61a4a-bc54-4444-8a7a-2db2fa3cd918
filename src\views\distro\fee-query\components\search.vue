<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <!-- 管理员 -->
    <el-form label-width="100px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="卡号">
            <el-input clearable v-model.trim="form.triggeredByCardNumber" placeholder="请输入名称" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="券号">
            <el-input clearable v-model.trim="form.triggeredByTicketNumber" placeholder="请输入名称" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="续期ID">
            <el-input clearable v-model.trim="form.renewalId" placeholder="请输入续期记录ID" />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="120px" label="计算时间">
            <el-date-picker type="datetimerange" start-placeholder="开始时间"
              end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd"
              time-format="A hh:mm:ss" />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="120px" label="佣金产生时间">
            <el-date-picker type="datetimerange" start-placeholder="开始时间"
              end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd"
              time-format="A hh:mm:ss" />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="120px" label="结算到余额时间">
            <el-date-picker  type="datetimerange" start-placeholder="开始时间"
              end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd"
              time-format="A hh:mm:ss" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="状态">
            <el-select v-model="form.status" clearable placeholder="请选择">
              <el-option label="待结算" value="PendingSettlement" />
              <el-option label="已结算" value="Settled" />
              <el-option label="结算中" value="IncludedInWithdrawal" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商ID">
            <el-input clearable v-model.trim="form.username" placeholder="请输入ID" />
          </el-form-item>
        </el-col>
        
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-link type="primary" :underline="false" @click="toggleMore">
              <span class="mx-2">更多</span>
              <el-icon v-if="!more">
                <IconElArrowDown />
              </el-icon>
              <el-icon v-else>
                <IconElArrowUp />
              </el-icon>
            </el-link>
          </el-form-item>
        </el-col>
      </el-row>
      
    </el-form>
  </ele-card>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { useFormData } from "@/utils/use-form-data";

/** 定义事件 */
const emit = defineEmits(["search"]);

/** 表单数据 */
const [form, resetFields] = useFormData({
  renewalTimeStart: null,
  renewalTimeEnd: null,
  settlementTimeStart: null,
  settlementTimeEnd: null,
  status: null,
  cardNumber: null,

  renewalId: null,
  calculationTimeStart: null,
  calculationTimeEnd: null, 
  triggeredByCardNumber: null,
  triggeredByTicketNumber: null,
});

/** 搜索 */
const search = () => {
  emit("search", { ...form });
};

/** 重置 */
const reset = () => {
  resetFields();
  search();
};


/** 更多 */
const more = ref(false);
const toggleMore = () => {
  more.value = !more.value;
};

onMounted(() => {

});
</script>
