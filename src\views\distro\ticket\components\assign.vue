<template>
    <el-dialog v-model="visible" :title="title" width="500px" :destroy-on-close="true"
        @open="handleOpen" @closed="handleClosed">
        <el-form :model="form" label-width="100px">
            <el-form-item v-if="actionType === 'assign'" label="分发给" required>
                <vendor-select v-model="form.targetVendorId" placeholder="请选择目标分销商"
                               child-type="direct" :parent-vendor-id="parentVendorId"
                               :level="targetVendorLevel"
                               style="width: 100%" />
                <ele-text type="placeholder" size="sm">
                    只能分发给下级分销商
                </ele-text>
            </el-form-item>

            <template v-if="numberType === 'range'">
                <el-form-item label="起始券号" required>
                    <el-input v-model="form.startNum" placeholder="请输入起始券号" />
                </el-form-item>
                <el-form-item label="结束券号" required>
                    <el-input v-model="form.endNum" placeholder="请输入结束券号" />
                </el-form-item>
            </template>

            <template v-if="numberType === 'specified'">
                <el-form-item label="已选择券号">
                    <el-input v-model="form.ticketNumber" disabled type="textarea" :rows="3" placeholder="已选择的券号" />
                </el-form-item>
            </template>

            <el-form-item label="券数量">
                <el-input v-model="form.ticketCount" :disabled="numberType === 'specified'" />
            </el-form-item>

            <el-form-item label="备注">
                <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="submitAssign" :loading="submitting">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import VendorSelect from '@/components/VendorSelect/index.vue';
import { requestAssignTicket, requestCancelAssignTicket } from '@/api/distro/ticket/index';
import { EleMessage } from 'ele-admin-plus/es';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    selectedRows: {
        type: Array,
        default: () => []
    },
    mode: {
        type: String,
        default: 'assign' // 'assign', 'cancelAssign'
    }
});

const emit = defineEmits(['update:visible', 'done']);

const parentVendorId = ref('');
const targetVendorLevel = ref(1);

// 控制弹窗显示
const visible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
});

// 分发方式
const actionType = ref('assign');       // assign, cancelAssign
const numberType = ref('specified');    // specified, range

// 监听模式变化
watch(() => props.mode, (val) => {
    actionType.value = val;
});

const title = computed(() => {
    return actionType.value === 'assign' ? '分发实体券' : '回收实体券';
});

// 提交状态
const submitting = ref(false);

// 表单数据
const form = reactive({
    targetVendorId: '',
    ticketCount: 0,
    startNum: '',
    endNum: '',
    ticketNumber: '',
    remark: '',
    productId: ''
});

/** 提交分发 */
const submitAssign = async () => {
    if (actionType.value === 'assign' && !form.targetVendorId) {
        EleMessage.error('请选择目标分销商');
        return;
    }

    // 构建请求参数
    const params = {
        productId: form.productId,
        ticketCount: form.ticketCount,
        targetVendorId: form.targetVendorId,
        remark: form.remark
    };

    if (numberType.value === 'specified') {
        const rows = props.selectedRows;
        if (!rows.length) {
            EleMessage.error('请选择需要分发的实体券');
            return;
        }

        // 再次检查是否都是可用状态
        const unavailableTickets = rows.filter(item => item.status !== 'Available');
        if (unavailableTickets.length > 0) {
            EleMessage.warning(`选中的券中有${unavailableTickets.length}张不是可用状态，无法分发`);
            return;
        }

        params.ticketNumber = rows.map(item => item.ticketNumber).join(',');
    } else {
        // 号段分发
        if (!form.startNum) {
            EleMessage.error('请输入起始券号');
            return;
        }
        if (!form.endNum) {
            EleMessage.error('请输入结束券号');
            return;
        }
        if (form.startNum > form.endNum) {
            EleMessage.error('起始券号不能大于结束券号');
            return;
        }
        if (!form.ticketCount) {
            EleMessage.error('请输入券数量');
            return;
        }

        params.startNum = form.startNum;
        params.endNum = form.endNum;
    }

    try {
        submitting.value = true;

        if (actionType.value === 'assign') {
            await requestAssignTicket(params);
        } else {
            await requestCancelAssignTicket(params);
        }
        EleMessage.success('操作成功');
        visible.value = false;
        emit('done');
    } catch (e) {
        console.error(e);
        EleMessage.error(e.message || '操作失败');
    } finally {
        submitting.value = false;
    }
};

const handleOpen = () => {
    const rows = props.selectedRows;
    if (rows && rows.length > 0) {
        form.ticketCount = rows.length;
        form.ticketNumber = rows.map(item => item.ticketNumber).join(',');
        
        // 当选择了一张券时，自动转为range模式
        if (rows.length === 1) {
            numberType.value = 'range';
            form.startNum = rows[0].ticketNumber;
            form.endNum = rows[0].ticketNumber;
        }

        // 目标分销商只能选择当前分销商的子分销商
        parentVendorId.value = rows[0].vendorId;
        form.productId = rows[0].productId;
        if (!parentVendorId.value) {
            // 如果券还没有分发，只能选择1级分销商
            targetVendorLevel.value = 1;
        } 
    } else {
        EleMessage.warning('请选择需要分发的实体券');
    }
}

/** 弹窗关闭时重置表单 */
const handleClosed = () => {
    form.targetVendorId = '';
    form.startNum = '';
    form.endNum = '';
    form.remark = '';
    form.ticketCount = 0;
    form.ticketNumber = '';

    numberType.value = 'specified';
    targetVendorLevel.value = null;
    parentVendorId.value = null;
};
</script>
