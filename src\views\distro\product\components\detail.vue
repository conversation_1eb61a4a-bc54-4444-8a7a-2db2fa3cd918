<template>
    <ele-drawer v-model="visible" title="产品详情" :size="600" style="max-width: 100%;" @open="open" @close="close">
        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">产品ID：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple"><ele-copyable>{{ detail.productId }}</ele-copyable></div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">产品名称：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.productName }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">产品编号：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple"><ele-copyable>{{ detail.productGid }}</ele-copyable></div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">产品描述：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.description }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">分佣规则：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple" v-if="detail.feeRuleSetName">
                    {{ detail.feeRuleSetName }} 
                    <ele-copyable>{{ detail.feeRuleSetId }}</ele-copyable>
                </div>
                <div class="grid-content ep-bg-purple" v-else>
                    未关联分佣规则
                </div>
            </el-col>
        </el-row>
  
        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">启用状态：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.isActive ? '已启用' : '未启用' }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">创建人：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">
                    <ele-copyable class="mr-4">{{ detail.createBy }}</ele-copyable>
                    {{ detail.createByUsername }}
                </div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">更新人：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">
                    <ele-copyable class="mr-4">{{ detail.updateBy }}</ele-copyable>
                    {{ detail.updateByUsername }}
                </div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">创建时间：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.createTime }}</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="6">
                <div class="grid-content f-r ep-bg-purple font-14 font-weight-500">更新时间：</div>
            </el-col>
            <el-col :span="18">
                <div class="grid-content ep-bg-purple">{{ detail.updateTime }}</div>
            </el-col>
        </el-row>
    </ele-drawer>
</template>

<script setup>
import { defineProps, defineEmits, computed, reactive } from 'vue';
import { requestProductDetail } from '@/api/distro/product/index';
import { ElMessage } from 'element-plus';

const props = defineProps({
    visible: { type: Boolean, default: false },
    data: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['close'])

const visible = computed({
    get() { return props.visible },
    set() { }
})

let detail = reactive({
    createBy: "",
    createTime: "",
    description: "",
    isActive: false,
    productId: "",
    productGid: "",
    productName: "",
    updateBy: "",
    updateTime: "",
});

async function open() {
    try {
        // 重置 detail 数据，确保每次打开时数据都是最新的
        Object.assign(detail, {
            createBy: "",
            createTime: "",
            description: "",
            isActive: false,
            productId: "",
            productGid: "",
            productName: "",
            updateBy: "",
            updateTime: "",
        });
        let { productId } = props.data;
        if (!productId) {
            throw { message: '获取编号失败' }
        }
        let result = await requestProductDetail({ productId })
        Object.assign(detail, result); // 合并数据到 detail 中，覆盖已有属性，新增属性会被添加到 detail 中
    } catch (error) {
        let content = error.message || '获取数据失败';
        ElMessage.error(content);
    }
}

function close() {
    emit('close');
}
</script>