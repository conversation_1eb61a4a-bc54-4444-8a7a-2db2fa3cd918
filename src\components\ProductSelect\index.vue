<template>
  <el-select
    :model-value="modelValue"
    @update:model-value="handleUpdate"
    :placeholder="placeholder"
    filterable
    clearable
    v-bind="$attrs"
  >
    <el-option
      v-for="item in productList"
      :key="item.productId"
      :label="item.productName"
      :value="item.productId"
    />
  </el-select>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { requestProductList } from '@/api/distro/product/index';

const props = defineProps({
  modelValue: [String, Number, null],
  placeholder: {
    type: String,
    default: '请选择产品'
  }
});
const emit = defineEmits(['update:modelValue']);

const productList = ref([]);

const fetchProductList = async () => {
  const result = await requestProductList({ page: 1, pageSize: 9999 });
  productList.value = result.rows;
};

onMounted(fetchProductList);

const handleUpdate = (val) => {
  emit('update:modelValue', val);
};

watch(
  () => props.modelValue,
  val => emit('update:modelValue', val)
);
</script> 