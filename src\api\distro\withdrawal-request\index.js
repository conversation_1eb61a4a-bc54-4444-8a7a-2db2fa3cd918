import request from '@/utils/request';


const BASE_API = '/distro/withdrawal-request';

/**
 * @async
 * @description 查询提现申请记录列表 (分页)
 * @param {Object} params - 查询参数
 * @param {String} [params.vendorName] - 按分销商名称查询 (可选)
 * @param {String} [params.requestTimeStart] - 申请时间范围起始 (格式: yyyy-MM-dd HH:mm:ss) (可选)
 * @param {String} [params.requestTimeEnd] - 申请时间范围结束 (格式: yyyy-MM-dd HH:mm:ss) (可选)
 * @param {String} [params.status] - 按状态筛选 (常用 'Pending', 'Approved') (可选)
 * @param {Number} [params.page] - 页码 (默认1) (可选)
 * @param {Number} [params.pageSize] - 每页数量 (默认10) (可选)
 */
export async function requestWithdrawalRequestList(params) {
    const res = await request.get(BASE_API + '/list', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/** 
 * @async
 * @description 获取提现申请详细信息 (分销商只能看自己的，管理员可看所有)。
 * @param {Object} params - 查询参数
 * @param {String} params.requestId - 申请ID (必填)
 */
export async function requestWithdrawalRequestDetail(params) {
    const res = await request.get(BASE_API + '/detail', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 提交提现申请。
 * @param {Object} params - 提交参数
 * @param {BigDecimal} params.requestedAmount - 申请提现金额 (必填)
 * @param {String} params.bankAccountInfo - 本次使用的银行账户信息 (JSON格式, 可能需要先绑定或选择已绑定的) (必填)
 * @param {String} [params.remark] - 申请备注 (可选)
 */
export async function requestWithdrawalRequestApply(params) {
    const res = await request.post(BASE_API + '/apply', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 管理员批准提现申请。
 * @param {Object} params - 提交参数
 * @param {String} params.requestId - 要批准的申请ID (必填)
 * @param {String} [params.remark] - 审批意见 (可选)
 */
export async function requestWithdrawalRequestApprove(params) {
    const res = await request.post(BASE_API + '/approve', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 管理员拒绝提现申请。
 * @param {Object} params - 提交参数
 * @param {String} params.requestId - 要拒绝的申请ID (必填)
 * @param {String} params.rejectionReason - 拒绝理由 (必填)
 */
export async function requestWithdrawalRequestReject(params) {
    const res = await request.post(BASE_API + '/reject', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 管理员标记提现申请为已支付。
 * @param {Object} params - 提交参数
 * @param {String} params.requestId - 已打款的申请ID (必填)
 * @param {String} [params.transactionReference] - 银行转账流水号或凭证号 (可选)
 * @param {String} [params.completionTime] - 实际打款完成时间 (格式: yyyy-MM-dd HH:mm:ss, 可选, 默认当前时间)
 */
export async function requestWithdrawalRequestMarkPaid(params) {
    const res = await request.post(BASE_API + '/mark-paid', { params });
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * @async
 * @description 管理员/用户取消提现申请。
 * @param {Object} params - 提交参数
 * @param {String} params.requestId - 要取消的申请ID (必填) 
 */
export async function requestWithdrawalRequestCancel(params) {
    const res = await request.post(BASE_API + '/cancel', params);
    if (res.data.code === 200) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 提交提现申请
 * @param {Object} params 参数
 */
export async function requestWithdrawalAdd(params) {
    const res = await request.post(BASE_API + '/add', params);
    return res.data;
}
