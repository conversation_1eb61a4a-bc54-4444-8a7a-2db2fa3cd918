<!-- 提现申请弹窗 -->
<template>
  <ele-modal form :width="560" v-model="visible" title="申请提现" @open="handleOpen" destroy-on-close>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" @submit.prevent="">
      <el-form-item label="申请金额" prop="requestedAmount">
        <el-input-number 
          v-model="form.requestedAmount" 
          :precision="2" 
          :step="100"
          :min="0"
          style="width: 100%"
          placeholder="请输入申请金额" />
      </el-form-item>
      <el-form-item label="银行账户" prop="bankAccountInfo">
        <el-input 
          type="textarea"
          v-model="form.bankAccountInfo" 
          :rows="4"
          placeholder="请输入银行账户信息，包括开户行、账号、户名等" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input 
          type="textarea"
          v-model="form.remark" 
          :rows="3"
          placeholder="请输入备注信息（选填）" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        提交申请
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import { requestWithdrawalRequestApply } from '@/api/distro/withdrawal-request/index';

/** 定义事件 */
const emit = defineEmits(['update:modelValue', 'done']);

/** 弹窗是否打开 */
const visible = defineModel('modelValue');

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields] = useFormData({
  requestedAmount: null,
  bankAccountInfo: '',
  remark: ''
});

/** 表单验证规则 */
const rules = reactive({
  requestedAmount: [
    {
      required: true,
      message: '请输入申请金额',
      trigger: 'blur'
    }
  ],
  bankAccountInfo: [
    {
      required: true,
      message: '请输入银行账户信息',
      type: 'string',
      trigger: 'blur'
    }
  ],
  remark: [
    {
      required: false,
      type: 'string',
      trigger: 'blur'
    }
  ]
});

/** 关闭弹窗 */
const handleCancel = () => {
  emit('update:modelValue', false);
};

/** 保存提交 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;

    const params = {
      ...form
    };

    requestWithdrawalRequestApply(params)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

/** 弹窗打开事件 */
const handleOpen = () => {
  resetFields();
};
</script> 