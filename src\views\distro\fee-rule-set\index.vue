<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="productId" :columns="columns" :datasource="feeRuleSetList"
        :pagination="true" :show-overflow-tooltip="false">
        <template #toolbar>
          <el-button type="primary" class="ele-btn-icon" :icon="PlusOutlined" @click="openEdit()">
            新建
          </el-button>
        </template>

        <template #feeType="{ row }">
          <span>{{ FeeType[row.feeType] }}</span>
        </template>

        <template #feeValue1="{ row }">
          <span v-if="row.feeType === 'Percentage'">{{ row.feeValue1 }}%</span>
          <span v-if="row.feeType === 'FixedAmount'">{{ row.feeValue1 }}</span>
        </template>

        <template #feeValue2="{ row }">
          <span v-if="row.feeType === 'Percentage'">{{ row.feeValue2 }}%</span>
          <span v-if="row.feeType === 'FixedAmount'">{{ row.feeValue2 }}</span>
        </template>

        <template #feeValue3="{ row }">
          <span v-if="row.feeType === 'Percentage'">{{ row.feeValue3 }}%</span>
          <span v-if="row.feeType === 'FixedAmount'">{{ row.feeValue3 }}</span>
        </template>

        <template #productList="{ row }">
          <div v-for="(product, index) in row.productList" :key="index">
            {{ product.productName }}
          </div>
        </template>

        <template #isDefault="{ row }">
          <el-switch v-model="row.isDefault" :disabled="row.isDefault" :loading="switchLoading" inline-prompt
            active-text="开启" inactive-text="关闭" :active-value="true" :inactive-value="false"
            :before-change="() => handlerDefaultChange(row)" />
        </template>

        <template #action="{ row }">
          <template v-if="row.username !== 'admin'">
            <!-- <el-link v-permission="'system:user:remove'" type="primary" :underline="false" @click="showBatch(row)">
              查看
            </el-link> -->
            <!-- <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" /> -->
            <el-link v-permission="'system:user:edit'" type="primary" :underline="false" @click="openEdit(row)">
              修改
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <el-link v-permission="'system:user:remove'" type="danger" :underline="false" @click="removeBatch(row)">
              删除
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <Edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 详情内容 - 抽屉 -->
    <Detail :visible="showDetail" :data="current" @close="onClose" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import {
  PlusOutlined,
} from '@/components/icons';
import { ElMessageBox } from 'element-plus/es';
import { EleMessage } from 'ele-admin-plus/es';
import Search from './components/search.vue';
import Edit from './components/edit_.vue';
import Detail from './components/detail.vue';
import { requestFeeRuleList, requestFeeRuleSetDefault, requestFeeRuleDelete } from '@/api/distro/fee-rule/index';
/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'ruleSetName',
    label: '规则名称',
    sortable: 'custom',
    align: 'left',
  },
  {
    prop: 'ruleSetId',
    label: '规则集编号',
    sortable: 'custom',
    align: 'center',
    hideInTable: true,
    width: 200
  },
  {
    prop: 'description',
    label: '描述',
    align: 'left',
    slot: 'description'
  },
  {
    prop: 'feeType',
    label: '佣金类型',
    value: 'descend',
    align: 'center',
    minWidth: 40,
    slot: 'feeType',
  },
  {
    prop: 'feeValue1',
    label: '1级分佣',
    align: 'center',
    minWidth: 40,
    slot: 'feeValue1',
  },
  {
    prop: 'feeValue2',
    label: '2级分佣',
    align: 'center',
    minWidth: 40,
    slot: 'feeValue2',
  },
  {
    prop: 'feeValue3',
    label: '3级分佣',
    align: 'center',
    minWidth: 40,
    slot: 'feeValue3',
  },
  {
    prop: 'productList',
    label: '关联产品',
    align: 'center',
    minWidth: 120,
    slot: 'productList',
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    sortable: 'custom',
    width: 160,
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 120,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 佣金类型 */
const FeeType = {
  Percentage: '百分比', // 百分比
  FixedAmount: '固定金额', // 固定金额
  NoFee: 'NoFee' // 无
}

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

const switchLoading = ref(false)

/** 是否显示详情 */
let showDetail = ref(false);

/** 表格数据源 */
const feeRuleSetList = ({ pages, where, orders, fliters }) => {
  return requestFeeRuleList({ ...pages, ...where, ...orders, ...fliters })
};

const handlerDefaultChange = async (val) => {
  let { ruleSetId } = val;
  switchLoading.value = true
  await requestFeeRuleSetDefault({ ruleSetId })
  reload()
  switchLoading.value = false
  return Promise.resolve(true)
}

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};
/** 批量删除 */
const removeBatch = (row) => {
  const { ruleSetId, ruleSetName } = row;
  if (!ruleSetId) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '是否确认删除【' + ruleSetName + '】的数据项?',
    '系统提示',
    { type: 'warning', draggable: true }
  )
    .then(() => {
      const loading = EleMessage.loading({
        message: '请求中..',
        plain: true
      });
      requestFeeRuleDelete({ ruleSetId })
        .then(() => {
          loading.close();
          EleMessage.success('删除成功');
          reload();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    })
    .catch(() => { });
};

/** 查看数据 */
// const showBatch = (row) => {
//   current.value = row ?? null;
//   console.log(row, '####');

//   // 获取id
//   showDetail.value = true;
// }

/** 关闭详情 */
const onClose = () => {
  showDetail.value = false;
}
</script>
