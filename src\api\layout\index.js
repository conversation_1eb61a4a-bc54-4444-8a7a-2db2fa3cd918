import request from '@/utils/request';
import { handleResponse } from '@/utils/request';

/**
 * 获取当前登录用户的个人信息/菜单/权限/角色
 * @returns {Promise<Object>} 用户信息，包含domainList域列表
 */
export function getUserInfo() {
  return handleResponse(request.get('/api/system/login-info'));
}

/**
 * 修改当前登录用户的密码
 * @param {Object} data 密码数据
 * @returns {Promise<string>} 成功消息
 */
export function updatePassword(data) {
  return handleResponse(request.put('/api/system/password', data), false) || '修改成功';
}

/**
 * 修改当前登录用户的个人信息
 * @param {Object} data 用户信息
 * @returns {Promise<Object>} 更新后的用户信息
 */
export function updateUserInfo(data) {
  return handleResponse(request.put('/auth/user', data));
}
