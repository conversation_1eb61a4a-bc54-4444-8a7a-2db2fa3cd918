#Include %A_ScriptDir%\FindText.ahk

SetTitleMatchMode 2

; 加载微信图标文本描述的函数
LoadWechatIconText()
{
    FileRead, WechatIconText, e:\wechat.txt
    if (ErrorLevel)
    {
        MsgBox, 0, 错误, 无法读取微信图标描述文件 e:\wechat.txt！
        return ""
    }
    return WechatIconText
}

; alt + w，显示微信消息（从 e:/wechat.txt 加载微信图标的文本描述，然后查找并点击它）
!w::
WechatIconText := LoadWechatIconText()
if (WechatIconText = "")
{
    return
}

Loop, 5
{
    if (ok:=FindText(2197-150000, 1406-150000, 2197+150000, 1406+150000, 0, 0, WechatIconText))
    {
        CoordMode, Mouse
        X:=ok.1.x, Y:=ok.1.y, Comment:=ok.1.id
        MouseGetPos, nowX, nowY   
        MouseClick, , %X%, %Y%
        MouseMove , %nowX%, %nowY%, 0
        break
    }
    else
    {
        Sleep, 150
    }
}

return 