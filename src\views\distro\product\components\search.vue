<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="100px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="产品名称">
            <el-input clearable v-model.trim="form.productName" placeholder="请输入名称" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="产品编号">
            <el-input clearable v-model.trim="form.productGid" placeholder="请输入编号" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="是否启用">
            <el-select v-model="form.isActive" placeholder="请选择" clearable>
              <el-option label="全部" value="" />
              <el-option label="已启用" :value="true" />
              <el-option label="未启用" :value="false" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { onMounted } from "vue";
import { useFormData } from "@/utils/use-form-data";

/** 定义事件 */
const emit = defineEmits(["search"]);

/** 表单数据 */
const [form, resetFields] = useFormData({
  productName: null,
  productGid: null,
  isActive: null,
});

/** 搜索 */
const search = () => {
  emit("search", { ...form });
};

/** 重置 */
const reset = () => {
  resetFields();
  search();
};


/** 更多 */
// const more = ref(false);
// const toggleMore = () => {
//   more.value = !more.value;
// };

onMounted(() => {

});
</script>
