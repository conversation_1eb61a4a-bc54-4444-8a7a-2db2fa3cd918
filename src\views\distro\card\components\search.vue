<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="60px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="卡号" prop="cardNumber">
            <el-input clearable v-model.trim="form.cardNumber" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="券号" prop="activationTicketNumber">
            <el-input clearable v-model.trim="form.activationTicketNumber" placeholder="请输入券号" />
          </el-form-item>
        </el-col>
        
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商" prop="vendorId">
            <vendor-select v-model="form.vendorId" placeholder="请选择分销商" />
          </el-form-item>
        </el-col>

        <el-col v-if="more" :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="姓名" prop="customerName">
            <el-input clearable v-model.trim="form.customerName" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>

        <el-col v-if="more" :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="身份证" prop="idCard">
            <el-input clearable v-model.trim="form.idCard" placeholder="请输入身份证" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-link type="primary" :underline="false" @click="toggleMore">
              <span class="mx-2">更多</span>
              <el-icon v-if="!more">
                <IconElArrowDown />
              </el-icon>
              <el-icon v-else>
                <IconElArrowUp />
              </el-icon>
            </el-link>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { ref } from "vue";
import Dayjs from "dayjs";
import { useFormData } from "@/utils/use-form-data";
import VendorSelect from "@/components/VendorSelect/index.vue";

/** 定义事件 */
const emit = defineEmits(["search"]);

/** 表单数据 */
const [form, resetFields] = useFormData({
  cardNumber: null,
  customerName: null,
  idCard: null,
  activationTicketNumber: null,
  status: null,
  expiryDate: [],
  expiryDateStart: null,
  expiryDateEnd: null,
  vendorId: null,
});

/** 搜索 */
const search = () => {
  console.log(form.expiryDate, 'card-time');
  if(form.expiryDate.length > 1){
    form.expiryDateStart = Dayjs(form.expiryDate[0]).format("YYYY-MM-DD HH:mm:ss");
    form.expiryDateEnd = Dayjs(form.expiryDate[1]).format("YYYY-MM-DD HH:mm:ss");
  }
  emit("search", { ...form });
};

/** 重置 */
const reset = () => {
  resetFields();
  search();
};


/** 更多 */
const more = ref(false);
const toggleMore = () => {
  more.value = !more.value;
};
</script>
