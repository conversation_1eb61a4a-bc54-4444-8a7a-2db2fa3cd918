<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="ledgerId"
        :columns="columns"
        :datasource="feeLedgerList"
        :pagination="true"
        :show-overflow-tooltip="false"
      >
        <template #distributionStatus="{ row }">
          <el-tag :type="getStatusType(row.distributionStatus)">
            {{ getStatusLabel(row.distributionStatus) }}
          </el-tag>
        </template>

        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="handleDetail(row)">
            查看
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>

    <!-- 详情内容 - 抽屉 -->
    <Detail :visible="showDetail" :data="current" @close="onClose" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import Search from './components/search.vue';
import Detail from './components/detail.vue';
import { requestFeeLedgerList } from '@/api/distro/fee-ledger';

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'ledgerId',
    label: '总账ID',
    align: 'center',
    width: 180, 
    hideInTable: true
  },
  {
    prop: 'renewalId',
    label: '续期记录ID',
    align: 'center',
    width: 180,
    hideInTable: true
  },
  {
    prop: 'cardNumber',
    label: '年卡卡号',
    align: 'left',
    width: 160
  },
  {
    prop: 'productName',
    label: '产品名称',
    align: 'center',
    width: 120
  },
  {
    prop: 'ticketNumber',
    label: '实体券号',
    align: 'left',
    width: 160
  }, 
  {
    prop: 'feeRuleSetName',
    label: '分佣规则',
    align: 'center',
    width: 120
  },  
  {
    prop: 'totalFee',
    label: '总佣金',
    align: 'right',
    width: 120,
    formatter: (row) => {
      return row.totalFee + ' 元';
    }
  },
  {
    prop: 'remark',
    label: '备注',
    align: 'left',
    minWidth: 120
  },
  {
    prop: 'calcTime',
    label: '计算时间',
    align: 'center',
    width: 160
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 120,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 获取状态类型 */
const getStatusType = (status) => {
  const types = {
    'Pending': 'warning',
    'Completed': 'success',
    'Failed': 'danger'
  };
  return types[status] || 'info';
};

/** 获取状态标签 */
const getStatusLabel = (status) => {
  const labels = {
    'Pending': '待分配',
    'Completed': '已完成',
    'Failed': '失败'
  };
  return labels[status] || status;
};

/** 当前查看的数据 */
const current = ref(null);

/** 是否显示详情 */
const showDetail = ref(false);

/** 表格数据源 */
const feeLedgerList = ({ pages, where }) => {
  return requestFeeLedgerList({
    ...pages,
    ...where
  });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 查看数据 */
const handleDetail = (row) => {
  current.value = row ?? {};
  showDetail.value = true;
};

/** 关闭详情 */
const onClose = () => {
  showDetail.value = false;
};
</script> 