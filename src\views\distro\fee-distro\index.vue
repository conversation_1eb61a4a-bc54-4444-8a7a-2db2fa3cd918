<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="distroId"
        :columns="columns"
        :datasource="feeDistroList"
        :pagination="true"
        :show-overflow-tooltip="false"
      >
        <template #status="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import Search from './components/search.vue';
import { requestFeeDistroList } from '@/api/distro/fee-distro';
import dayjs from 'dayjs';

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'distroId',
    label: '明细ID',
    align: 'center',
    width: 180,
    hideInTable: true
  },
  {
    prop: 'ledgerId',
    label: '总账ID',
    align: 'center',
    width: 180,
    hideInTable: true
  },
  {
    prop: 'cardNumber',
    label: '年卡卡号',
    align: 'left',
    width: 160
  },
  {
    prop: 'productName',
    label: '产品名称',
    align: 'center',
    width: 120
  },
  {
    prop: 'ticketNumber',
    label: '实体券号',
    align: 'left',
    minWidth: 160
  },
  {
    prop: 'vendorName',
    label: '分销商',
    align: 'center',
    width: 120
  },
  {
    prop: 'feeAmount',
    label: '佣金金额',
    align: 'right',
    width: 120,
    formatter: (row) => {
      return row.feeAmount + ' 元';
    }
  },
  {
    prop: 'renewalTime',
    label: '佣金产生时间',
    align: 'center',
    width: 160
  },
  {
    prop: 'settleTime',
    label: '结算时间',
    align: 'center',
    width: 160
  },  
]);

/** 获取状态类型 */
const getStatusType = (status) => {
  const types = {
    'PendingSettlement': 'warning',
    'Settled': 'success',
    'IncludedInWithdrawal': 'info'
  };
  return types[status] || 'info';
};

/** 获取状态标签 */
const getStatusLabel = (status) => {
  const labels = {
    'PendingSettlement': '待结算',
    'Settled': '已结算',
    'IncludedInWithdrawal': '已提现'
  };
  return labels[status] || status;
};

/** 表格数据源 */
const feeDistroList = ({ pages, where }) => {
  if (!where.settleTimeStart) {
    where.settleTimeStart = dayjs().subtract(2, 'day').format('YYYY-MM-DD');
  }
  if (!where.settleTimeEnd) {
    where.settleTimeEnd = dayjs().format('YYYY-MM-DD');
  }
  return requestFeeDistroList({
    ...pages,
    ...where
  });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};
</script> 