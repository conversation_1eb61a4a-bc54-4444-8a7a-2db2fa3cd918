<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="requestId" :columns="columns" :datasource="withDrawalList"
        :pagination="true" :show-overflow-tooltip="false">

        <template #toolbar>
          <el-button type="primary" plain class="ele-btn-icon" :icon="PlusOutlined" @click="handleApply">
            申请提现
          </el-button>
        </template>

        <template #status="{ row }">
          <dict-data type="text" code="withdrawalRequestStatus" v-model="row.status" />
        </template>

        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="handleDetail(row)">
            详情
          </el-link>
          <template v-if="row.status === 'Pending'">
            <el-divider direction="vertical" />
            <el-popconfirm title="确定要取消该提现申请吗？" @confirm="handleCancel(row)" :width="220">
              <template #reference>
                <el-link type="danger" :underline="false">
                  取消
                </el-link>
              </template>
            </el-popconfirm>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>

    <!-- 详情内容 - 抽屉 -->
    <Detail :visible="showDetailDrawer" :data="currentDetail" @close="onClose" />
    <!-- 提现申请弹窗 -->
    <Apply v-model="showApply" @done="reload" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import {
  PlusOutlined,
} from '@/components/icons';
import { EleMessage } from 'ele-admin-plus/es';
import Search from './components/search.vue';
import Detail from './components/detail.vue';
import Apply from './components/apply.vue';
import DictData from '@/components/DictData/index.vue';
import {
  requestWithdrawalRequestList,
  requestWithdrawalRequestCancel
} from '@/api/distro/withdrawal-request/index';

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'requestId',
    label: '申请ID',
    align: 'center',
    width: 180
  },
  {
    prop: 'vendorId',
    label: '分销商ID',
    align: 'center',
    hideInTable: true,
    minWidth: 110
  },
  {
    prop: 'requestedAmount',
    label: '申请金额',
    align: 'center',
    minWidth: 110
  },
  {
    prop: 'requestTime',
    label: '申请时间',
    align: 'center',
    minWidth: 110
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    minWidth: 100,
    slot: 'status'
  },
  {
    prop: 'processingTime',
    label: '审核时间',
    align: 'center',
    minWidth: 110
  },
  {
    prop: 'transferRemark',
    label: '转账备注',
    align: 'center',
    minWidth: 120
  },
  {
    prop: 'rejectionReason',
    label: '拒绝理由',
    align: 'center',
    minWidth: 120
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 180,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 是否显示详情抽屉 */
const showDetailDrawer = ref(false);

/** 是否显示提现申请弹窗 */
const showApply = ref(false);

/** 当前查看的详情数据 */
const currentDetail = ref(null);

/** 表格数据源 */
const withDrawalList = ({ pages, where, orders, filters }) => {
  return requestWithdrawalRequestList({
    ...pages,
    ...where,
    ...orders,
    ...filters,
  });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 显示详情 */
const handleDetail = (row) => {
  currentDetail.value = row;
  showDetailDrawer.value = true;
};

/** 关闭详情 */
const onClose = () => {
  showDetailDrawer.value = false;
};

/** 打开提现申请弹窗 */
const handleApply = () => {
  showApply.value = true;
};

/** 取消申请 */
const handleCancel = (row) => {
  requestWithdrawalRequestCancel({ requestId: row.requestId })
    .then((msg) => {
      EleMessage.success(msg);
      reload();
    })
    .catch((e) => {
      EleMessage.error(e.message);
    });
};
</script>

<style scoped>
.el-divider--vertical {
  margin: 0 8px;
}
</style>
