<!-- 编辑弹窗 -->
<template>
  <ele-modal form :width="560" v-model="visible" title="创建顶级实体券号段" @open="handleOpen" destroy-on-close>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" @submit.prevent="">
      <el-form-item label="关联产品" prop="productId">
        <el-input clearable v-model="form.productId" placeholder="请输入关联产品ID" />
      </el-form-item>
      <el-form-item label="起始券号" prop="startNumber">
        <el-input clearable v-model.number="form.startNumber" placeholder="请输入号段起始券号" />
      </el-form-item>
      <el-form-item label="结束券号" prop="endNumber">
        <el-input clearable v-model.number="form.endNumber" placeholder="请输入号段结束券号" />
      </el-form-item>
      <el-form-item label="总数" prop="totalCount">
        <el-input clearable v-model.number="form.totalCount" placeholder="请输入总数" />
      </el-form-item>
      <el-form-item label="是否可用" prop="isActive">
        <el-switch v-model="form.isActive" :active-value="true" :inactive-value="false" active-color="#13ce66" inactive-color="#ff4949" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input clearable v-model="form.description" resize="none" type="textarea" :rows="5" placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        创建
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import { requestTicketSegmentImport } from '@/api/distro/ticket-segment/index';

/** 修改回显的数据 */
const props = defineProps({
  data: Object
});

/** 定义事件 */
const emit = defineEmits(['update:modelValue', 'done']);

/** 弹窗是否打开 */
const visible = defineModel('modelValue');

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  productId: null,
  startNumber: null,
  totalCount: 0,
  isActive: false,
  endNumber: null,
  description: null
});

/** 表单验证规则 */
const rules = reactive({
  productId: [
    {
      required: true,
      message: '关联产品ID不能为空',
      type: 'string',
      trigger: 'blur'
    }
  ],
  startNumber: [
    {
      required: true,
      message: '号段起始券号不能为空',
      type: 'number',
      trigger: 'blur'
    }
  ],
  endNumber: [
    {
      required: true,
      message: '号段结束券号不能为空',
      type: 'number',
      trigger: 'blur'
    }
  ],
  totalCount: [
    {
      required: true,
      message: '总数不能为空',
      type:'number',
      trigger: 'blur'
    }
  ]
});

/** 关闭弹窗 */
const handleCancel = () => {
  emit('update:modelValue', false);
};

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }

    // 号段 16位
    if (form.startNumber.toString().length !== 16 || form.endNumber.toString().length !== 16) {
      EleMessage.error('券号必须为16位');
      return;
    }
    // 号段格式校验
    if (form.startNumber > form.endNumber) {
      EleMessage.error('起始券号不能大于结束券号');
      return;
    }
    // 总数校验
    if (form.totalCount !== (form.endNumber - form.startNumber + 1)) {
      EleMessage.error('总数与实际券号段不匹配');
      return;
    }

    loading.value = true;
    const saveOrUpdate = requestTicketSegmentImport;
    // 如果有密码字段且不为空，则加密
    const params = {
      ...form
    };

    console.log(params, 'params');
    saveOrUpdate(params)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

/** 弹窗打开事件 */
const handleOpen = () => {
  resetFields();
  const params = Object.assign({}, props.data);
  assignFields(params);
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  });
};
</script>
