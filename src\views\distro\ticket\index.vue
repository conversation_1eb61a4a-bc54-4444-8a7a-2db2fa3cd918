<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="ticketId" :columns="columns" :datasource="vendorList" :pagination="true"
        :show-overflow-tooltip="false" v-model:selections="selections" :row-selection="{ selectable: isSelectable }">
        <template #toolbar>
          <el-button v-permission="'distro:ticket:update'" type="primary" class="ele-btn-icon" plain
            @click="openAssignDialog(null, 'assign')" :disabled="selections.length === 0">
            批量分发
          </el-button>
          <el-button v-permission="'distro:ticket:update'" type="danger" class="ele-btn-icon" plain
            @click="openAssignDialog(null, 'cancelAssign')" :disabled="selections.length === 0">
            批量回收
          </el-button>
        </template>

        <template #status="{ row }">
          <el-tag :type="statusEnum[row.status].type">{{ statusEnum[row.status].label }}</el-tag>
        </template>

        <template #action="{ row }">
          <template v-if="row.username !== 'admin'">
            <el-link v-permission="'system:user:remove'" type="primary" :underline="false" @click="showBatch(row)">
              查看
            </el-link>
            <el-divider direction="vertical" />
            <el-link v-permission="'distro:ticket:assign'" type="primary" :underline="false"
              :disabled="!isAssignable(row)" @click="openAssignDialog(row, 'assign')">
              分发
            </el-link>
            <el-divider direction="vertical" />
            <el-link v-permission="'distro:ticket:cancelAssign'" type="danger" :underline="false"
              :disabled="!isCancelAssignable(row)" @click="openAssignDialog(row, 'cancelAssign')">
              回收
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 详情内容 - 抽屉 -->
    <Detail :visible="showDetail" :data="current" @close="onClose" />

    <!-- 分发弹窗 -->
    <Assign v-model:visible="showAssign" :selected-rows="assignRows" :mode="assignMode" @done="handleAssignDone" />
  </ele-page>
</template>

<script setup>
import { ref, reactive } from 'vue';
import Search from './components/search.vue';
import Detail from './components/detail.vue';
import Assign from './components/assign.vue';
import { requestTicketList } from '@/api/distro/ticket/index';
import { EleMessage } from 'ele-admin-plus/es';

/** 表格实例 */
const tableRef = ref(null);

const statusEnum = {
  'Available': {
    type: 'success',
    label: '可用'
  },
  'Activated': {
    type: 'primary',
    label: '已激活'
  },
  'Used': {
    type: 'danger',
    label: '已使用'
  },
}

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 50,
    align: 'center',
    fixed: 'left',    
  },
  {
    prop: 'ticketId',
    label: 'ID',
    align: 'center',
    hideInTable: true,
    width: 200
  },
  {
    prop: 'ticketNumber',
    label: '券号',
    align: 'left',
    width: 200,
  },
  {
    prop: 'productName',
    label: '产品名称',
    align: 'center',
    minWidth: 120,
  },
  {
    prop: 'vendorName',
    label: '分销商',
    align: 'center',
    minWidth: 120,
    formatter: (row) => {
      return row.vendorName ? `${row.vendorName} - ${row.vendorLevel}级` : '--'
    }
  },
  {
    prop: 'cardNumber',
    label: '关联卡号',
    align: 'center',
    width: 160,
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    minWidth: 40,
    slot: 'status',
    filterMultiple: false,
    filters: [
      {
        text: '可用',
        value: 'Available'
      },
      {
        text: '已激活',
        value: 'Activated'
      },
      {
        text: '已使用',
        value: 'Used'
      }
    ],
  }, 
  {
    prop: 'createTime',
    label: '创建时间',
    sortable: 'custom',
    width: 180,
    align: 'center',
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 160,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 是否显示详情 */
let showDetail = ref(false);

/** 表格选中数据 */
const selections = ref([]);

let current = reactive({
  ticketNumber: '',
  ticketId: '',
});

/** 表格数据源 */
const vendorList = ({ pages, where, orders, filters }) => {
  return requestTicketList({
    ...pages,
    ...where,
    ...orders,
    ...filters,
  });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 查看数据 */
const showBatch = (row) => {
  const { ticketId, ticketNumber } = row;
  current.ticketId = ticketId;
  current.ticketNumber = ticketNumber;
  // 获取id
  showDetail.value = true;
}

/** 关闭详情 */
const onClose = () => {
  showDetail.value = false;
}

/** 判断行是否可选择 */
const isSelectable = (row) => {
  return row.status === 'Available';
};

/** 分发相关数据 */
const showAssign = ref(false);
const assignRows = ref([]);
const assignMode = ref('assign');

/** 打开分发弹窗 */
const openAssignDialog = (row, mode) => {
  assignMode.value = mode;

  const isAssign = mode != 'cancelAssign';

  // 如果是单个券的分发操作，检查状态
  let rows = selections.value;
  if (row) {
    rows = [row];
  }

  // 批量操作时，检查是否有不可用的券  
  const checkMethod = isAssign ? isAssignable : isCancelAssignable;
  for(const item of rows) {
    if (!checkMethod(item)) {
      EleMessage.warning(`${item.ticketNumber} 不是${isAssign ? '可用' : '可回收'}状态，无法${isAssign ? '分发' : '回收'}`);
      return;
    }
  }

  // 必须是同一个产品
  const productId = rows[0].productId;
  if (rows.some(item => item.productId !== productId)) {
    EleMessage.error('选中的券必须是同一个产品');
    return;
  }

  // 必须是同一个分销商
  const vendorId = rows[0].vendorId;
  if (rows.some(item => item.vendorId !== vendorId)) {
    EleMessage.error('选中的券必须是同一个分销商');
    return;
  }

  // 必须都 assignable
  if (isAssign) {
    for (const item of rows) {
      if (!isAssignable(item)) {
        EleMessage.error(`无法再分发给下级分销商 : ${item.ticketNumber}`);
        return;
      }
    }
  } else {
    for (const item of rows) {
      if (!item.vendorId) {
        EleMessage.error(`无法回收券 : ${item.ticketNumber}`);
        return;
      }
    }
  }

  if (rows.length === 0) {
    EleMessage.warning('请选择需要分发的券');
    return;
  }

  assignRows.value = [...rows];
  showAssign.value = true;
};

/** 判断券是否可分发 */
const isAssignable = (row) => {
  const { status, vendorLevel } = row;
  if (status === 'Used') return false;
  if (vendorLevel && Number(vendorLevel) >= 3) return false;
  return true;
};

/** 判断券是否可回收 */
const isCancelAssignable = (row) => {
  const { status, vendorId } = row;
  if (status === 'Used') return false;
  if (!vendorId) return false;
  return true;
}

/** 分发完成处理 */
const handleAssignDone = () => {
  reload();
  selections.value = [];
  assignRows.value = [];
};
</script>
