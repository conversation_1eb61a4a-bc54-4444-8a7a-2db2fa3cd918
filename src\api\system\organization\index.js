import request from '@/utils/request';

/**
 * 分页查询机构
 */
export async function pageOrganizations(params) {
  const res = await request.get('/api/system/org/page', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询机构列表
 */
export async function listOrganizations(params) {
  const res = await request.get('/api/system/org/list', { params });
  if (res.data.code === 200 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加机构
 */
export async function addOrganization(data) {
  const res = await request.post('/api/system/org/add', data);
  if (res.data.code === 200) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 修改机构
 */
export async function updateOrganization(data) {
  const res = await request.post('/api/system/org/update', data);
  if (res.data.code === 200) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除机构
 */
export async function removeOrganization(id) {
  const res = await request.post('/api/system/org/delete',{id});
  if (res.data.code === 200) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
