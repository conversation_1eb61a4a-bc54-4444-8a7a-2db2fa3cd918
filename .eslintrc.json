{"root": true, "env": {"browser": true, "node": true, "es6": true}, "parser": "vue-eslint-parser", "extends": ["eslint:recommended", "plugin:vue/vue3-recommended", "plugin:prettier/recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "jsxPragma": "React", "ecmaFeatures": {"jsx": true}}, "rules": {"vue/attributes-order": "off", "vue/attribute-hyphenation": "off", "vue/v-on-event-hyphenation": "off", "vue/custom-event-name-casing": "off", "vue/multi-word-component-names": "off", "vue/no-setup-props-destructure": "off", "vue/script-setup-uses-vars": "error", "vue/one-component-per-file": "off", "vue/max-attributes-per-line": "off", "vue/html-closing-bracket-newline": "off", "vue/multiline-html-element-content-newline": "off", "vue/singleline-html-element-content-newline": "off", "vue/require-default-prop": "off", "vue/first-attribute-linebreak": ["error", {"singleline": "ignore", "multiline": "ignore"}], "vue/html-self-closing": ["error", {"html": {"void": "always", "normal": "never", "component": "always"}, "svg": "always", "math": "always"}], "no-use-before-define": "off", "space-before-function-paren": "off", "no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}]}}