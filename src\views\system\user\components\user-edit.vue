<!-- 编辑弹窗 -->
<template>
  <ele-modal form :width="460" v-model="visible" :title="isUpdate ? '修改用户' : '添加用户'" @open="handleOpen"
    destroy-on-close>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" @submit.prevent="">
      <el-form-item label="用户名" prop="username">
        <el-input clearable v-model="form.username" placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item label="姓名" prop="realName">
        <el-input clearable v-model="form.realName" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <dict-data code="sex" v-model="form.sex" type="radio" />
      </el-form-item>
      <el-form-item label="手机号" prop="mobilePhone">
        <el-input clearable v-model="form.mobilePhone" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="账号状态" prop="accountStatus">
        <dict-data code="accountStatus" v-model="form.accountStatus" type="radio" />
      </el-form-item>
      <el-form-item label="头像地址" prop="avatar">
        <el-input clearable v-model="form.avatar" placeholder="请输入头像地址" />
      </el-form-item>
      <el-form-item label="角色" prop="roleIds">
        <role-select v-model="form.roleIds" multiple />
      </el-form-item>
      <el-form-item label="分销商" prop="vendorId">
        <vendor-select v-model="form.vendorId" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import { useFormData } from '@/utils/use-form-data';
import { addUser, updateUser } from '@/api/system/user';
import RoleSelect from '@/components/RoleSelect/index.vue';
import VendorSelect from '@/components/VendorSelect/index.vue';
import { encryptByMd5 } from '@/utils/crypto';

/** 修改回显的数据 */
const props = defineProps({
  data: Object
});

/** 定义事件 */
const emit = defineEmits(['update:modelValue', 'done']);

/** 弹窗是否打开 */
const visible = defineModel('modelValue');

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: null,
  username: null,
  realName: null,
  sex: null,
  mobilePhone: null,
  accountStatus: 1,
  avatar: null,
  roleIds: [],
  teamIds: [],
  vendorId: null,
});

/** 表单验证规则 */
const rules = reactive({
  username: [
    {
      required: true,
      message: '请输入用户名',
      type: 'string',
      trigger: 'blur'
    }
  ],
  realName: [
    {
      required: true,
      message: '请输入姓名',
      type: 'string',
      trigger: 'blur'
    }
  ],
  sex: [
    {
      required: true,
      message: '请选择性别',
    }
  ],
  accountStatus: [
    {
      required: true,
      message: '请选择账号状态',
    }
  ],
});

/** 关闭弹窗 */
const handleCancel = () => {
  emit('update:modelValue', false);
};

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    const saveOrUpdate = isUpdate.value ? updateUser : addUser;
    // 如果有密码字段且不为空，则加密
    const userData = {
      ...form
    };
    if (userData.password) {
      userData.password = encryptByMd5(userData.password);
    }
    saveOrUpdate(userData)
      .then(() => {
        loading.value = false;
        EleMessage.success('操作成功');
        handleCancel();
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

/** 弹窗打开事件 */
const handleOpen = async () => {
  if (props.data) {
    const user = Object.assign({}, props.data);
    user.sex = String(user.sex);
    user.accountStatus = String(user.accountStatus);
    user.roleIds = user.roleList?.map(role => String(role.id));
    user.teamIds = user.teamList?.map(team => String(team.id));
    assignFields(user);
    isUpdate.value = true;
  } else {
    resetFields();
    isUpdate.value = false;
  }
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  });
};
</script>
