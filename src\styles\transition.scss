/** 路由切换动画 */

/* 执行动画时隐藏页脚 */
.fade-enter-active,
.fade-leave-active,
.slide-bottom-enter-active,
.slide-bottom-leave-active,
.slide-right-leave-active,
.slide-right-enter-active,
.zoom-in-enter-active,
.zoom-in-leave-active,
.zoom-out-leave-active,
.zoom-out-enter-active {
  & + .ele-footer {
    visibility: hidden;
  }
}

/* 渐变 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 底部消退 */
.slide-bottom-enter-active,
.slide-bottom-leave-active {
  transition: (opacity 0.2s ease-out, transform 0.25s ease-out);
}

.slide-bottom-enter-from {
  opacity: 0;
  transform: translateY(-10%);
}

.slide-bottom-leave-to {
  opacity: 0;
  transform: translateY(10%);
}

/* 右侧消退 */
.slide-right-leave-active,
.slide-right-enter-active {
  transition: (opacity 0.05s ease-out, transform 0.1s ease-out);
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-60px);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(60px);
}

/* 放大渐变 */
.zoom-in-enter-active,
.zoom-in-leave-active {
  transition: (opacity 0.2s ease-out, transform 0.25s ease-out);
}

.zoom-in-enter-from {
  opacity: 0;
  transform: scale(0.9);
}

.zoom-in-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

/* 缩小渐变 */
.zoom-out-leave-active,
.zoom-out-enter-active {
  transition: (opacity 0.2s ease-out, transform 0.25s ease-out);
}

.zoom-out-enter-from {
  opacity: 0;
  transform: scale(1.2);
}

.zoom-out-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

/* 对话框过渡动画 */

/* 对话框容器过渡时间 */
.el-dialog__wrapper {
	transition-duration: 0.3s;
}

/* 对话框进入时禁用默认动画 */
.dialog-fade-enter-active {  
	animation: none !important;
}

/* 对话框离开时设置过渡时间并禁用默认动画 */
.dialog-fade-leave-active {
	transition-duration: 0.15s !important;
	animation: none !important;
}

/* 对话框进入和离开时设置动画模式 */
.dialog-fade-enter-active .el-dialog,
.dialog-fade-leave-active .el-dialog{
	animation-fill-mode: forwards;
}

/* 对话框进入时的动画设置 */
.dialog-fade-enter-active .el-dialog{
	animation-duration: 0.3s;
	animation-name: anim-open;
	animation-timing-function: cubic-bezier(0.6,0,0.4,1);
}

/* 对话框离开时的动画设置 */
.dialog-fade-leave-active .el-dialog{
	animation-duration: 0.3s;
	animation-name: anim-close;
}

/* 对话框打开动画关键帧 */
@keyframes anim-open {
	0% { opacity: 0;  transform: scale3d(0, 0, 1); }
	100% { opacity: 1; transform: scale3d(1, 1, 1); }
}

/* 对话框关闭动画关键帧 */
@keyframes anim-close {
	0% { opacity: 1; }
	100% { opacity: 0; transform: scale3d(0.5, 0.5, 1); }
}

